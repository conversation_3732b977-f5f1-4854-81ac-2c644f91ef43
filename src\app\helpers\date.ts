export default {
  getWeekStartAndEndDates(weekLabel = "Current + 1"): {
    monday: string;
    sunday: string;
  } {
    const weekOrderNumber = +weekLabel.match(/\d+/)![0];
    const d = new Date();

    d.setUTCDate(
      d.getUTCDate() + 7 * (weekOrderNumber - 1) + ((7 - d.getUTCDay()) % 7) + 1
    );
    const mondayDate = d.toISOString().substring(0, 10);

    d.setUTCDate(d.getUTCDate() + 6);
    const sundayDate = d.toISOString().substring(0, 10);

    return { monday: mondayDate, sunday: sundayDate };
  },

  getMondayDate(weekLabel: string): string {
    const weekOrderNumber = +weekLabel.match(/\d+/)![0];
    const d = new Date();

    d.setUTCDate(
      d.getUTCDate() + 7 * (weekOrderNumber - 1) + ((7 - d.getUTCDay()) % 7) + 1
    );
    const mondayDate = d.toISOString().substring(0, 10);
    return mondayDate;
  },

  getLocalDateAndTime(separators = true) {
    const dt = new Date();
    const currentDayOfMonth = dt.getDate();
    const currentMonth = dt.getMonth() + 1;
    const currentYear = dt.getFullYear();
    const currentHours = dt.getHours();
    const currentMinutes = dt.getMinutes();
    const currentSeconds = dt.getSeconds();

    const currentDayOfMonthString =
      currentDayOfMonth < 10 ? "0" + currentDayOfMonth : currentDayOfMonth;
    const currentMonthString =
      currentMonth < 10 ? "0" + currentMonth : currentMonth;
    const currentHoursString =
      currentHours < 10 ? "0" + currentHours : currentHours;
    const currentMinutesString =
      currentMinutes < 10 ? "0" + currentMinutes : currentMinutes;
    const currentSecondsString =
      currentSeconds < 10 ? "0" + currentSeconds : currentSeconds;

    let dateSeparator = "-";
    let timeSeparator = ".";

    if (!separators) {
      dateSeparator = "";
      timeSeparator = "";
    }

    const date = `${currentYear}${dateSeparator}${currentMonthString}${dateSeparator}${currentDayOfMonthString}`;
    const time = `${currentHoursString}${timeSeparator}${currentMinutesString}${timeSeparator}${currentSecondsString}`;

    return `${date}T${time}`;
  },

  convertUtcDateToIsoFormat(date, format) {
    format = format.toLowerCase().trim();

    const dateParts = [
      ["y", format.indexOf("y")],
      ["m", format.indexOf("m")],
      ["d", format.indexOf("d")],
    ]
      .sort(([_partId1, index1], [_partId2, index2]) => index1 - index2)
      .map(([partId, _index]) => partId);

    let year, month, day;
    date.split(/[^\d]/g).map((part, index) => {
      switch (dateParts[index]) {
        case "y":
          year = part;
          break;
        case "m":
          month = part - 1;
          break;
        case "d":
          day = part;
          break;
      }
    });

    const dateObj = new Date(Date.UTC(year, month, day));

    return dateObj.toISOString().substring(0, 10);
  },

  getDayOfWeek(day: string): number {
    const daysOfWeek = [
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
      "Sunday",
    ];
    return daysOfWeek.findIndex((element) => element === day);
  },
};
