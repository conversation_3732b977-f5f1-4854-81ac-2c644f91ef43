import cypressHelper from "@/app/helpers/cypress";

export default {
  createAgency(agency) {
    const options = {
      method: "POST",
      url: "app-api/agency",
      body: agency,
    };

    return cypressHelper.apiCall(options).then((response) => response["body"]);
  },
  setUserAgency(userId, agencyId) {
    const options = {
      method: "PUT",
      url: `app-api/authusers/${userId}/update-auth-user-agency`,
      body: { agencyId }
    };
    return cypressHelper
      .apiCall(options)
      .then((response) => response["body"]);
  },
  createClient(client) {
    const options = {
      method: "POST",
      url: "app-api/clients",
      body: client,
    };
    return cypressHelper
      .apiCall(options)
      .then((response) => response["body"]);
  },
};
