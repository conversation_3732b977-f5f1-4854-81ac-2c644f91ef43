import Sdt from "@/app/sdt";

/**
 * SDT (Script Driven Testing) Entry Point
 *
 * This file serves as the main entry point for the SDT framework, which is a Cypress-based
 * testing system that supports dynamic extension loading and test execution.
 *
 * Key responsibilities:
 * 1. Initialize the SDT framework with the appropriate extension
 * 2. Set up Cypress test lifecycle hooks (before, beforeEach, afterEach, after)
 * 3. Execute the test suite
 *
 * The SDT framework supports multiple project extensions that are dynamically loaded
 * based on the 'app' environment variable.
 */

/**
 * Initialize and run the SDT framework
 *
 * This IIFE (Immediately Invoked Function Expression) handles the complete lifecycle
 * of SDT initialization and execution within the Cypress environment.
 */
(async (): Promise<void> => {
  try {
    // Create and initialize the SDT instance
    const sdt = new Sdt();

    // Initialize the SDT framework and attach it to the global Cypress object
    // This makes the SDT instance available throughout the test execution
    Cypress.sdt = await sdt.initialize();

    // Validate that initialization was successful
    if (!Cypress.sdt) {
      throw new Error(
        "SDT initialization failed: sdt instance is null or undefined"
      );
    }

    // Validate that required setup methods are available
    if (!Cypress.sdt.setup) {
      throw new Error(
        "SDT initialization failed: setup methods are not available"
      );
    }

    // Set up Cypress test lifecycle hooks
    // These hooks are called by Cypress at specific points during test execution

    /**
     * Before hook - runs once before all tests
     * Typically used for:
     * - Setting up test result folders
     * - Initializing global test configuration
     * - Preparing the test environment
     */
    before(() => {
      try {
        Cypress.sdt.setup.before();
      } catch (error) {
        console.error("Error in before hook:", error);
        throw error;
      }
    });

    /**
     * BeforeEach hook - runs before each individual test
     * Typically used for:
     * - Resetting the domain state
     * - Clearing previous test data
     * - Setting up fresh test context
     */
    beforeEach(() => {
      try {
        Cypress.sdt.setup.beforeEach();
      } catch (error) {
        console.error("Error in beforeEach hook:", error);
        throw error;
      }
    });

    /**
     * AfterEach hook - runs after each individual test
     * Typically used for:
     * - Taking screenshots on test failure
     * - Cleaning up test artifacts
     * - Recording test results
     */
    afterEach(() => {
      try {
        Cypress.sdt.setup.afterEach();
      } catch (error) {
        console.error("Error in afterEach hook:", error);
        // Don't re-throw here to avoid masking the original test failure
      }
    });

    /**
     * After hook - runs once after all tests complete
     * Typically used for:
     * - Generating final test reports
     * - Cleaning up global resources
     * - Finalizing test execution
     */
    after(() => {
      try {
        //Cypress.sdt.setup.after();
      } catch (error) {
        console.error("Error in after hook:", error);
        // Don't re-throw here to avoid interfering with test completion
      }
    });

    // Start the test execution
    // This method creates Cypress describe/it blocks for all configured tests
    Cypress.sdt.run();
  } catch (error) {
    // Enhanced error logging with more context
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorStack = error instanceof Error ? error.stack : undefined;

    console.error("Failed to initialize SDT:", errorMessage);
    if (errorStack) {
      console.error("Stack trace:", errorStack);
    }

    // Re-throw to ensure Cypress fails the test run
    throw new Error(`SDT initialization failed: ${errorMessage}`);
  }
})();
