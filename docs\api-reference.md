# API Reference

This document provides a comprehensive reference for all available actions, functions, and utilities in the SDT framework.

## Core Actions

Core actions are available in all extensions and provide fundamental test automation capabilities.

### Navigation Actions

#### Navigate
Navigates to a specified URL.
- **Usage**: `Navigate | URL | | x`
- **Parameters**: URL in Values column
- **Example**: `Navigate | | https://example.com | x`

#### Back
Navigates back in browser history.
- **Usage**: `Back | | | x`
- **Parameters**: None

#### Reload
Reloads the current page.
- **Usage**: `Reload | | | x`
- **Parameters**: None

### Input Actions

#### Type
Types text into an input field.
- **Usage**: `Type | Element | Text | x`
- **Parameters**: 
  - Target: Element name or selector
  - Values: Text to type
- **Example**: `Type | Username | admin | x`

#### Clear
Clears the content of an input field.
- **Usage**: `Clear | Element | | x`
- **Parameters**: Target: Element name or selector
- **Example**: `Clear | Username | | x`

#### Click
Clicks on an element.
- **Usage**: `Click | Element | | x`
- **Parameters**: Target: Element name or selector
- **Example**: `Click | Login Button | | x`

#### Select
Selects an option from a dropdown.
- **Usage**: `Select | Element | Option | x`
- **Parameters**:
  - Target: Dropdown element
  - Values: Option text or value
- **Example**: `Select | Country | United States | x`

### Validation Actions

#### Check
Validates element properties and content.
- **Usage**: `Check | Element | Expected Value | x`
- **Parameters**:
  - Target: Element to check
  - Values: Expected value or property
- **Special Values**:
  - `notexist` - Verify element doesn't exist
  - `visible` - Verify element is visible
  - `hidden` - Verify element is hidden
- **Example**: `Check | Welcome Message | Welcome, Admin! | x`

#### Wait
Waits for a specified time or condition.
- **Usage**: `Wait | | Seconds | x`
- **Parameters**: Values: Wait time in seconds (default: 2)
- **Special Targets**:
  - `spinner` - Wait for spinner to disappear
- **Example**: `Wait | | 5 | x`

#### Wait Interceptor
Waits for an API interceptor to complete.
- **Usage**: `Wait Interceptor | | | x`
- **Parameters**: None
- **Note**: Requires prior API interceptor setup

### Test Control Actions

#### Start Test
Marks the beginning of a test case.
- **Usage**: `Start Test | | | x`
- **Parameters**: None
- **Required**: Every test must start with this action

#### End Test
Marks the end of a test case.
- **Usage**: `End Test | | | x`
- **Parameters**: None
- **Required**: Every test must end with this action

#### Start Script
Marks the beginning of a script.
- **Usage**: `Start Script | | | x`
- **Parameters**: None
- **Required**: Every script must start with this action

#### End Script
Marks the end of a script.
- **Usage**: `End Script | | | x`
- **Parameters**: None
- **Required**: Every script must end with this action

### Utility Actions

#### Screenshot
Takes a screenshot at the current step.
- **Usage**: `Screenshot | | | x`
- **Parameters**: None

#### LogToConsole
Logs information to the browser console.
- **Usage**: `LogToConsole | | Message | x`
- **Parameters**: Values: Message to log

#### Pause
Pauses test execution for debugging.
- **Usage**: `Pause | | | x`
- **Parameters**: None
- **Note**: Only works in interactive mode

### Database Actions

#### Drop DB
Drops the test database.
- **Usage**: `Drop DB | | | x`
- **Parameters**: None
- **Warning**: This will delete all data in the test database

## Core Functions

Functions can be called from test data using bracket notation: `[FunctionName(parameters)]`

### Date Functions

#### Date
Calculates dates relative to today.
- **Syntax**: `[Date(period, unit, format)]`
- **Parameters**:
  - `period`: Number of units to add/subtract (default: "1")
  - `unit`: Time unit - "d" (days), "wd" (working days), "w" (weeks), "m" (months), "y" (years)
  - `format`: Date format string (default: "MM/DD/YYYY")
- **Examples**:
  - `[Date(1, d)]` - Tomorrow
  - `[Date(-7, d)]` - Last week
  - `[Date(1, m, "yyyy-mm-dd")]` - Next month in ISO format

### Random Functions

#### Random
Generates random numbers.
- **Syntax**: `[Random(min, max)]`
- **Parameters**:
  - `min`: Minimum value
  - `max`: Maximum value
- **Example**: `[Random(1000, 9999)]` - Random 4-digit number

## Extension-Specific Actions

Extensions can provide additional actions specific to their applications.

### Common Extension Actions

#### Create User
Creates a new user (available in most extensions).
- **Usage**: `Create User | | UserData | x`
- **Parameters**: Values: User data object or table reference

#### Login User
Logs in a user (available in most extensions).
- **Usage**: `Login User | | Credentials | x`
- **Parameters**: Values: Login credentials

#### Create Customer
Creates a customer record (business applications).
- **Usage**: `Create Customer | | CustomerData | x`
- **Parameters**: Values: Customer data object

## Data References

### Table References
Reference data from Tables sheet using bracket notation.
- **Syntax**: `[TableName][RowId][FieldName]`
- **Example**: `[Users][User1][Username]` returns the username for User1

### Alias References
Reference aliases using double angle brackets.
- **Syntax**: `«alias-name»`
- **Example**: `«admin-email»` returns the aliased email address

### Cross-References
Tables can reference other table data.
- **Example**: `[Table1][Id1][Field1]` can contain `[Table2][Id2][Field2]`

## Element Targeting

### Element Names
Use element names defined in the Elements sheet.
- **Example**: `Username` (references element defined in Elements sheet)

### Direct Selectors
Use CSS selectors or XPath directly.
- **CSS**: `#username`, `.login-button`, `input[type="password"]`
- **XPath**: `//button[text()='Login']`

### Hierarchical Elements
Use root elements for scoped selection.
- **Format**: `RootElement > ChildElement`
- **Example**: `Header > User Menu`

## Error Handling

### Common Error Scenarios

#### Element Not Found
- **Error**: Element selector doesn't match any elements
- **Solution**: Verify element exists and selector is correct

#### Timeout Errors
- **Error**: Action times out waiting for element or condition
- **Solution**: Increase timeout or add explicit waits

#### API Errors
- **Error**: API calls fail or return unexpected responses
- **Solution**: Check API endpoints and response handling

### Debugging Actions

#### Check Element Existence
Use `Check` action with `notexist` to verify elements don't exist.
- **Example**: `Check | Error Message | notexist | x`

#### Conditional Execution
Use `Do` column to conditionally execute steps.
- **Values**: `x` = execute, empty = skip

## Best Practices

### Action Usage

1. **Use Descriptive Names**: Choose clear, descriptive action names
2. **Handle Errors**: Use Check actions to validate expected outcomes
3. **Wait Appropriately**: Add waits for dynamic content
4. **Modular Design**: Use scripts for reusable action sequences

### Data Management

1. **Use Tables**: Store test data in Tables sheet
2. **Leverage Aliases**: Use aliases for repeated text
3. **Reference Properly**: Use correct bracket notation for references

### Test Structure

1. **Start/End Actions**: Always use Start Test/End Test
2. **Logical Grouping**: Group related actions in scripts
3. **Clear Flow**: Maintain clear test execution flow

## Advanced Features

### Custom Actions
Extensions can define custom actions specific to their applications.

### API Integration
Use apiHandler methods for backend integration.

### Database Operations
Use task methods for database operations.

### Screenshot Control
Control screenshot behavior with configuration options.

## Configuration Options

### Screenshot Settings
- `takeScreenshotOnError`: "yes" | "no"
- `takeScreenshotOnTitle`: "yes" | "no"

### Timeout Settings
- `defaultCommandTimeout`: Milliseconds for command timeout
- `pageLoadTimeout`: Milliseconds for page load timeout

### Browser Settings
- `viewportWidth`: Browser viewport width
- `viewportHeight`: Browser viewport height
- `chromeWebSecurity`: Enable/disable Chrome web security

For more detailed configuration options, see the [Configuration Guide](configuration.md).
