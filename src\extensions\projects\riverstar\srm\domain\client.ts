import h from "@/app/helpers/all";

export default class Client {
  id = "";
  firstName = "";
  lastName = "";
  birthDate = "";
  gender = "";
  address = "";
  address2 = "";
  city = "";
  state = "";
  county = "";
  zip = "";
  email = "";
  insurancePrimary_planName = "";
  insuranceSecondary_planName = "";
  insuranceTertiary_planName = "";
  insurancePrimary_policyNum = "";
  insuranceSecondary_policyNum = "";
  insuranceTertiary_policyNum = "";
  insurancePrimary_Type = "";
  insuranceSecondary_Type = "";
  insuranceTertiary_Type = "";
  pcpName = "";
  pcpNpi = "";
  medicaidId = "";
  commonKey = "";
  clientTeam = [];
  contactPreference = "";
  contacts = [];
  migrationId = "";
  phone = "";
  workPhone = "";
  homePhone = "";
  externalClientId = "";

  constructor(client) {
    Object.assign(this, h.parseObjectWithTemplate(client, this));
    this.birthDate = h.convertUtcDateToIsoFormat(this.birthDate, "mm/dd/yyyy");
    this.gender = this.gender.toLowerCase().trim();
    this.address = client["Address"];
    this.address2 = client["Address (cont.)"];
    this.city = client["City"];
    this.state = client["State Initials"];
    this.county = client["County Code"];
    this.zip = client["Zip"];
    if (client["Mobile Phone"])
      this.contacts.push({
        contact: client["Mobile Phone"].match(/\d/g)?.join(""),
        contactType: "mobile",
      });
    if (client["Home Phone"])
      this.contacts.push({
        contact: client["Home Phone"].match(/\d/g)?.join(""),
        contactType: "home",
      });
    if (client["Work Phone"])
      this.contacts.push({
        contact: client["Work Phone"].match(/\d/g)?.join(""),
        contactType: "work",
      });
    this.contactPreference = client["Contact Preference"]?.toLowerCase().trim();
  }

  create() {
    return Cypress.sdt.apiHandler
      .login(
        Cypress.sdt.config.admin.username,
        Cypress.sdt.config.admin.password
      )
      .then(() => Cypress.sdt.apiHandler.createClient(this));
  }
}
