# Extension Development Guide

This guide explains how to create custom extensions for the SDT (Script Driven Tests) framework.

## Overview

Extensions in SDT provide application-specific functionality, custom actions, API handlers, and UI configurations. They allow you to extend the core framework with project-specific capabilities while maintaining a clean separation of concerns.

## Extension Architecture

### Extension Structure

Each extension is organized in the following structure:

```
projects/
├── organization/
│   └── project/
│       └── extension/
│           ├── sdt.ts              # Main extension export
│           ├── core/
│           │   ├── actions.ts      # Custom actions
│           │   ├── icons.ts        # UI icons configuration
│           │   └── setup.ts        # Test lifecycle hooks
│           ├── handlers/
│           │   └── apiHandler.ts   # API integration
│           ├── domain/
│           │   └── *.ts           # Domain models
│           └── tasks.ts           # Background tasks (optional)
```

### Extension Interface

Extensions must implement the `ISdtExtension` interface:

```typescript
interface ISdtExtension {
  setup?: {
    before?: () => void;
    beforeEach?: () => void;
    afterEach?: () => void;
    after?: () => void;
  };
  config?: Record<string, any>;
  icons?: Record<string, any>;
  actions?: Record<string, any>;
  functions?: Record<string, any>;
  apiHandler?: Record<string, any>;
  spyHandler?: Record<string, any>;
  apiInterceptors?: Record<string, any>;
}
```

## Creating an Extension

### 1. Main Extension File (sdt.ts)

Create the main extension file that exports your extension configuration:

```typescript
import extensionActions from "./core/actions";
import extensionApiHandler from "./handlers/apiHandler";
import extensionIcons from "./core/icons";
import extensionSetup from "./core/setup";
import h from "@/app/helpers/all";
import organizationSdt from "@organizations/your-org/sdt";

export default {
  setup: extensionSetup,
  config: organizationSdt.config,
  icons: h.mergeObjects(organizationSdt.icons, extensionIcons),
  actions: h.mergeObjects(organizationSdt.actions, extensionActions),
  functions: organizationSdt.functions,
  apiHandler: h.mergeObjects(organizationSdt.apiHandler, extensionApiHandler),
} as const;
```

### 2. Custom Actions (core/actions.ts)

Define custom actions specific to your application:

```typescript
export default {
  "Create User": function() {
    const userData = Cypress.sdt.current.step.simpleValues[0];
    // Implementation logic
    return Cypress.sdt.apiHandler.createUser(userData);
  },

  "Login User": function() {
    const credentials = Cypress.sdt.current.step.simpleValues[0];
    // Implementation logic
    cy.visit('/login');
    cy.get('#username').type(credentials.username);
    cy.get('#password').type(credentials.password);
    cy.get('#login-btn').click();
  },

  "Verify Dashboard": function() {
    // Implementation logic
    cy.url().should('include', '/dashboard');
    cy.get('.welcome-message').should('be.visible');
  }
};
```

### 3. API Handler (handlers/apiHandler.ts)

Create API integration functions:

```typescript
export default {
  createUser: (userData: any) => {
    const options = {
      method: "POST",
      url: "/api/users",
      body: userData,
      failOnStatusCode: true,
    };
    return cy.request(options).then((response) => response.body);
  },

  getUser: (userId: string) => {
    const options = {
      method: "GET",
      url: `/api/users/${userId}`,
      failOnStatusCode: true,
    };
    return cy.request(options).then((response) => response.body);
  },

  deleteUser: (userId: string) => {
    const options = {
      method: "DELETE",
      url: `/api/users/${userId}`,
      failOnStatusCode: true,
    };
    return cy.request(options);
  }
};
```

### 4. Icons Configuration (core/icons.ts)

Define UI icons for different element types:

```typescript
export default {
  Button: {
    "Add User": { left: "add" },
    "Delete User": { left: "delete" },
    "Save": { left: "save" },
    "Cancel": { left: "cancel" }
  },
  "Input With Label": {
    "Search": { left: "search" },
    "Filter": { left: "filter" }
  },
  "Menu Option": {
    "Admin Panel": { left: "arrow_right" },
    "Settings": { left: "arrow_right" }
  }
};
```

### 5. Setup Hooks (core/setup.ts)

Define test lifecycle hooks:

```typescript
export default {
  before() {
    // Run once before all tests
    cy.task('setupTestDatabase');
  },

  beforeEach() {
    // Run before each test
    cy.task('clearTestData');
    cy.visit('/');
  },

  afterEach() {
    // Run after each test
    cy.task('captureTestResults');
  },

  after() {
    // Run once after all tests
    cy.task('cleanupTestDatabase');
  }
};
```

## Domain Models

Create domain models to represent your application entities:

```typescript
// domain/user.ts
export default class User {
  id: string = "";
  username: string = "";
  email: string = "";
  firstName: string = "";
  lastName: string = "";

  constructor(data: any) {
    Object.assign(this, data);
  }

  create() {
    return Cypress.sdt.apiHandler.createUser(this);
  }

  login() {
    cy.visit('/login');
    cy.get('#username').type(this.username);
    cy.get('#password').type(this.password);
    cy.get('#login-btn').click();
  }

  delete() {
    return Cypress.sdt.apiHandler.deleteUser(this.id);
  }
}
```

## Extension Registration

### 1. Register in Main SDT Class

Add your extension to the extension map in `src/app/sdt.ts`:

```typescript
private static readonly extensionMap: Record<
  string,
  () => Promise<ExtensionModule>
> = {
  // ... existing extensions
  "your-app": () => import("../../projects/your-org/your-app/extension/sdt"),
};
```

### 2. Configuration

Create a configuration file for your project:

```json
{
  "organization": "your-organization",
  "app": "your-app",
  "appUrl": "http://localhost:3000",
  "appScript": "npm start",
  "sdtFile": "your-app-tests.xlsx",
  "takeScreenshotOnError": "yes",
  "takeScreenshotOnTitle": "no"
}
```

## Advanced Features

### Custom Functions

Add custom functions that can be called from test data:

```typescript
// In your extension's functions object
functions: {
  generateTestEmail: (prefix: string = "test") => {
    const timestamp = Date.now();
    return `${prefix}-${timestamp}@example.com`;
  },

  formatCurrency: (amount: number, currency: string = "USD") => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  }
}
```

### Database Tasks

Create background tasks for database operations:

```typescript
// tasks.ts
export default {
  setupTestData: async () => {
    // Database setup logic
    await global.db.collection('users').deleteMany({});
    await global.db.collection('users').insertMany(testUsers);
  },

  cleanupTestData: async () => {
    // Cleanup logic
    await global.db.collection('users').deleteMany({ isTest: true });
  }
};
```

### API Interceptors

Set up API interceptors for testing:

```typescript
apiInterceptors: {
  setupUserApiInterceptor: () => {
    cy.intercept('POST', '/api/users', { fixture: 'user.json' }).as('createUser');
    cy.intercept('GET', '/api/users/*', { fixture: 'user.json' }).as('getUser');
  }
}
```

## Best Practices

### 1. Organization Structure

- Keep extensions organized by organization and project
- Use consistent naming conventions
- Separate concerns (actions, API handlers, domain models)

### 2. Action Design

- Make actions atomic and focused
- Use descriptive action names
- Handle errors gracefully
- Support both direct parameters and step values

### 3. API Integration

- Use consistent error handling
- Implement proper response validation
- Support both synchronous and asynchronous operations
- Use Cypress commands for API calls

### 4. Domain Models

- Create models for complex entities
- Include business logic in models
- Use TypeScript for better type safety
- Support serialization/deserialization

### 5. Configuration

- Use environment-specific configurations
- Keep sensitive data in environment variables
- Document all configuration options
- Provide sensible defaults

## Testing Your Extension

### 1. Unit Testing

Test individual components:

```typescript
// Test your actions
describe('Custom Actions', () => {
  it('should create user successfully', () => {
    const userData = { username: 'test', email: '<EMAIL>' };
    // Test implementation
  });
});
```

### 2. Integration Testing

Test the complete extension:

```typescript
// Test extension loading
describe('Extension Integration', () => {
  it('should load extension correctly', () => {
    // Test extension loading and initialization
  });
});
```

## Debugging Extensions

### 1. Logging

Use console logging for debugging:

```typescript
console.log('Extension loaded:', extensionName);
console.log('Action executed:', actionName, parameters);
```

### 2. Cypress Debugging

Use Cypress debugging tools:

```typescript
cy.pause(); // Pause execution
cy.debug(); // Open debugger
```

### 3. Error Handling

Implement proper error handling:

```typescript
try {
  // Extension logic
} catch (error) {
  console.error('Extension error:', error);
  throw error;
}
```

## Example Extensions

The SDT framework includes several example extensions:

- **Riverstar Extensions** - CE, Billing, PWV, SRM applications
- **Dorian Solutions** - JBA (Job Board Aggregator)
- **Other** - RWA (Real World App) for demos

Study these extensions to understand implementation patterns and best practices.
