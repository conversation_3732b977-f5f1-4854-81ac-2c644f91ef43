import h from "@/app/helpers/all";

export default class User {
  id = "";
  firstName = "";
  middleName = "";
  lastName = "";
  email = "";
  username = "";
  profileName = "";
  profile = "";
  isActive = true;
  phone = "";
  notes = "";
  password = "";
  acceptedTermsAndConditions = true;
  isSelfService = false;
  ssoLookup = "";
  tempPassword = "";
  tempPasswordInEmail = "";

  constructor(data) {
    Object.assign(this, h.parseObjectWithTemplate(data, this));
    if (this.tempPasswordInEmail?.toLowerCase().trim() == "yes")
      this.tempPasswordInEmail = "yes";
    else this.tempPasswordInEmail = "no";
  }

  create() {
    const { username, profileName } = this;
    return cy
      .task("dbReadProfile", profileName)
      .then((profile) => {
        if (!profile) {
          throw new Error(`Profile not found: ${profileName}`);
        }
        this.profile = profile["_id"];
      })
      .then(() =>
        cy.task("dbReadUser", { username }).then((existingUser) => {
          if (existingUser) this.id = existingUser["_id"];
          else this.createRiverstarUser();
        })
      );
  }

  private createRiverstarUser() {
    Cypress.sdt.apiHandler
      .login(
        Cypress.sdt.config.admin.username,
        Cypress.sdt.config.admin.password
      )
      .then(() => Cypress.sdt.apiHandler.createUser(this))
      .then((user) => (this.id = user._id))
      .then(() => this.getTempPassword())
      .then(() => {
        Cypress.sdt.apiHandler.login(this.username, this.tempPassword);
      })
      .then(() => Cypress.sdt.apiHandler.resetPassword(this.password));
  }

  private getTempPassword() {
    if (this.tempPasswordInEmail === "yes") {
      return this.getPasswordFromEmail();
    } else {
      return this.getPasswordThroughResetPassword();
    }
  }

  private getPasswordFromEmail() {
    const PASSWORD_OFFSET = 9;
    const PASSWORD_LENGTH = 15;
    return cy.task<string>("dbGetLastEmail").then((emailText) => {
      if (!emailText) {
        throw new Error("Email not found");
      }
      const passwordIndex = emailText.indexOf("password=");
      if (passwordIndex === -1) {
        throw new Error("Password not found in email");
      }
      return emailText.substr(passwordIndex + PASSWORD_OFFSET, PASSWORD_LENGTH);
    });
  }

  private getPasswordThroughResetPassword() {
    return Cypress.sdt.apiHandler
      .adminResetPassword(this)
      .then(() => this.tempPassword);
  }

  login() {
    if (!this.username || !this.password) {
      throw new Error("Username and password are required for login");
    }
    return Cypress.sdt.apiHandler.login(this.username, this.password);
  }
}
