import h from "@/app/helpers/all";

export default {
  setRealmId() {
    cy.task("getAccessToken", Cypress.sdt.config.refreshToken)
      .then((response) => {
        Cypress.sdt.config["accessToken"] = response["access_token"];
      })
      .then(() => {
        cy.task("dbDeleteCollection", "quickbooks");
      })
      .then(() => {
        cy.task("dbCreateQuickbooks", {
          refreshToken: Cypress.sdt.config.refreshToken,
          accessToken: Cypress.sdt.config["accessToken"],
        });
      });
  },

  getQbCustomers() {
    h.consoleLog("qbHandler getQbCustomers 1");
    const startPosition = 1;
    const maxResults = 10;
    const customers = [];

    return getCustomerBag(startPosition).then(() => {
      h.consoleLog("qbHandler getQbCustomers 2", { customers });
      return customers;
    });

    function getCustomerBag(startPosition) {
      return cy
        .task("getCustomersBag", {
          accessToken: Cypress.sdt.config["accessToken"],
          startPosition,
          maxResults,
        })
        .then((customersBag: Array<object>) => {
          h.consoleLog("qbHandler getQbCustomers 3", {
            customersBag,
          });
          if (customersBag) {
            customers.push(...customersBag);
          }
          startPosition += maxResults;
          if (customersBag?.length === maxResults) {
            getCustomerBag(startPosition);
          }
        });
    }
  },

  createQbCustomer(customer) {
    h.consoleLog("qbHandler createQbCustomer 1", customer);
    return cy
      .task("setCustomer", {
        accessToken: Cypress.sdt.config["accessToken"],
        customer,
      })
      .then((response) => {
        h.consoleLog("qbHandler createQbCustomer 2", { response });
        return response;
      });
  },

  setQbCustomerInactive(customer) {
    h.consoleLog("qbHandler setQbCustomerInactive 1", customer);
    return cy
      .task("setCustomer", {
        accessToken: Cypress.sdt.config["accessToken"],
        customer: {
          Id: customer["Id"],
          SyncToken: customer["SyncToken"],
          Active: false,
          sparse: true,
        },
      })
      .then((response) => {
        h.consoleLog("qbHandler setQbCustomerInactive 2", {
          response,
        });
        return response;
      });
  },

  setAllQbCustomersInactive() {
    return this.getQbCustomers().then((customers) => {
      h.consoleLog("qbHandler setAllQbCustomersInactive", {
        customers,
      });
      customers.forEach((customer) => {
        this.setQbCustomerInactive(customer).then((response) => {
          h.consoleLog(
            `qbHandler setAllQbCustomersInactive 2 - ${customer.DisplayName}`,
            {
              response,
            }
          );
        });
      });
    });
  },

  getQbEmployees() {
    h.consoleLog("qbHandler getQbEmployees 1");
    return cy
      .task("getEmployees", Cypress.sdt.config["accessToken"])
      .then((employees) => {
        h.consoleLog("qbHandler getQbEmployees 2", { employees });
        return employees;
      });
  },

  createQbEmployee(employee) {
    h.consoleLog("qbHandler createQbEmployee 1", { employee });
    return cy
      .task("setEmployee", {
        accessToken: Cypress.sdt.config["accessToken"],
        employee,
      })
      .then((response) => {
        h.consoleLog("qbHandler createQbEmployee 2", { response });
        return response;
      });
  },

  setQbEmployeeInactive(employee) {
    h.consoleLog("qbHandler setQbEmployeeInactive 1", {
      employee,
    });
    return cy
      .task("setEmployee", {
        accessToken: Cypress.sdt.config["accessToken"],
        employee: {
          GivenName: employee["GivenName"],
          FamilyName: employee["FamilyName"],
          Id: employee["Id"],
          SyncToken: employee["SyncToken"],
          Active: false,
          sparse: true,
        },
      })
      .then((response) => {
        h.consoleLog("qbHandler setQbEmployeeInactive 2", {
          response,
        });
        return response;
      });
  },

  setAllQbEmployeesInactive() {
    return this.getQbEmployees().then((employees) => {
      h.consoleLog("qbHandler setAllQbEmployeesInactive 1", {
        employees,
      });
      if (!employees || !employees.length) {
        return [];
      }
      
      return employees.reduce((promise, employee) => {
        return promise.then(() => 
          this.setQbEmployeeInactive(employee).then((response) => {
            h.consoleLog(
              `qbHandler setAllQbEmployeesInactive 2 - ${employee.DisplayName}`,
              {
                response,
              }
            );
            return response;
          })
        );
      }, Promise.resolve());
    });
  },

  getQbVendors() {
    h.consoleLog("qbHandler getQbVendors 1");
    return cy.task("getVendors", Cypress.sdt.config["accessToken"]).then((vendors) => {
      h.consoleLog("qbHandler getQbVendors 2", { vendors: vendors });
      return vendors;
    });
  },

  createQbVendor(vendor) {
    h.consoleLog("qbHandler createQbVendor 1", { vendor });
    return cy
      .task("setVendor", {
        accessToken: Cypress.sdt.config["accessToken"],
        vendor,
      })
      .then((response) => {
        h.consoleLog("qbHandler createQbVendor 2", { response });
        return response;
      });
  },

  setQbVendorInactive(vendorDisplayName) {
    h.consoleLog("qbHandler setQbVendorInactive 1", {
      vendorDisplayName,
    });
    return cy
      .task("getVendor", {
        accessToken: Cypress.sdt.config["accessToken"],
        vendorDisplayName,
      })
      .then((vendor) => {
        h.consoleLog("qbHandler setQbVendorInactive 2", { vendor });
        return cy.task("setVendor", {
          accessToken: Cypress.sdt.config["accessToken"],
          vendor: {
            Id: vendor["Id"],
            SyncToken: vendor["SyncToken"],
            DisplayName: vendor["DisplayName"],
            Active: false,
            sparse: true,
          },
        });
      })
      .then((response) => {
        h.consoleLog("qbHandler setQbVendorInactive 3", { response });
        return response;
      });
  },

  setAllQbVendorsInactive() {
    return Cypress.sdt.qbHandler.getQbVendors().then((vendors) => {
      h.consoleLog("qbHandler setAllQbVendorsInactive 1", { vendors });
      vendors?.forEach((vendor) => {
        Cypress.sdt.qbHandler
          .setQbVendorInactive(vendor.DisplayName)
          .then((response) => {
            h.consoleLog("qbHandler setAllQbVendorsInactive 2", {
              response,
            });
          });
      });
    });
  },

  deleteAllQbInvoices() {
    h.consoleLog("qbHandler deleteAllQbInvoices 1");
    return cy
      .task("getInvoices", Cypress.sdt.config["accessToken"])
      .then((invoices: Array<object>) => {
        h.consoleLog("qbHandler deleteAllQbInvoices 2", {
          invoices: invoices,
        });
        invoices?.forEach((invoice) => {
          cy.task("deleteInvoice", {
            accessToken: Cypress.sdt.config["accessToken"],
            invoice: invoice,
          });
        });
      });

  },

  getQbTimeEntries() {
    h.consoleLog("qbHandler getQbTimeEntries 1");
    return cy
      .task("getTimeEntries", Cypress.sdt.config["accessToken"])
      .then((timeEntries) => {
        h.consoleLog("qbHandler getQbTimeEntries 2", {
          timeEntries: timeEntries,
        });
        return timeEntries;
      });
  },

  deleteAllQbTimeEntries() {
    h.consoleLog("qbHandler deleteAllQbTimeEntries 1");
    return cy
      .task("getTimeEntries", Cypress.sdt.config["accessToken"])
      .then((timeEntries: Array<object>) => {
        h.consoleLog("qbHandler deleteAllQbTimeEntries 2", {
          timeEntries: timeEntries,
        });
        timeEntries?.forEach((timeEntry) => {
          cy.task("deleteTimeEntry", {
            accessToken: Cypress.sdt.config["accessToken"],
            timeEntry,
          });
        });
      });
  },
};
