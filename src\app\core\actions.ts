import Element from "@/app/core/element";
import Entry from "@/app/core/entry";
import Target from "@/app/core/target";
import cypressHelper from "@/app/helpers/cypress";
import h from "@/app/helpers/all";
import path from "path";

/**
 * Actions module for Cypress SDT (Software Development Testing) framework
 * Provides a comprehensive set of actions for UI testing automation
 *
 * @module Actions
 * @description Contains all available actions that can be performed during test execution
 */
export default {
  /** Current window URL for cross-origin navigation tracking */
  currentWindowUrl: "",

  // ****************************************************************
  // Page Elements Actions
  // ****************************************************************

  /**
   * Validates element existence, visibility, and properties
   * Supports special validation for input errors and table rows
   *
   * @description Performs various checks on elements including:
   * - Element existence/non-existence
   * - Input field validation errors
   * - Table row counting
   * - Standard property validation
   */
  Check: function () {
    // Check for non-existence validation
    if (
      Cypress.sdt.current.step.simpleValues?.find(
        (value: string) =>
          h.compareNormalizedStrings(value, "notexist") ||
          h.compareNormalizedStrings(value, "notexists")
      )
    ) {
      const selector = Cypress.sdt.current.step.target.lastElement.selector;
      const content = Cypress.sdt.current.step.target.lastElement.content;
      return cy
        .contains(`${selector}:contains(${content})`)
        .should(($elem: JQuery<HTMLElement>) => {
          if (!$elem || $elem.length === 0) return expect($elem).to.not.exist;
          else return expect($elem).to.be.hidden;
        });
    }

    let validation = standardValidation;

    // Special validation for input field errors
    if (
      Cypress.sdt.current.step.target?.lastElementNameIs(
        "Input With Label Error"
      )
    ) {
      validation = ($elem: JQuery<HTMLElement>) => {
        const $formField = $elem.closest("mat-form-field");
        const firstError = $formField.find("mat-error")[0];
        const secondError = $formField.find("mat-error")[1];
        let errorText = firstError ? firstError.innerText.trim() : "";
        errorText = secondError ? secondError.innerText.trim() : errorText;
        expect(errorText).to.equal(Cypress.sdt.current.step.simpleValues[0]);
      };
    }
    // Special validation for table row counting
    else if (Cypress.sdt.current.step.target?.lastElementNameIs("Table Row")) {
      validation = ($elem: JQuery<HTMLElement>) => {
        const itemsNamedValue = Cypress.sdt.current.step.namedValues.find(
          (namedValue: any) => namedValue.left.includes("items")
        );
        const itemsNumber = itemsNamedValue?.right.match(/(\d+)/);
        if (itemsNumber) {
          expect($elem.length).to.equal(parseInt(itemsNumber[0]));
        }
      };
    }

    Cypress.sdt.current.step.target.get({ validation });
  },
  /**
   * Validates the mouse cursor shape when hovering over an element
   * @description Hovers over the target element and checks the CSS cursor property
   */
  "Check Mouse Pointer": function () {
    const shape = Cypress.sdt.current.step.simpleValues[0] ?? "pointer";
    Cypress.sdt.current.step.target
      .get()
      .realHover("mouse")
      .should("have.css", "cursor", shape);
  },

  /**
   * Validates tooltip text appears when hovering over an element
   * @description Hovers over the target element and checks for tooltip content
   */
  "Check Tooltip": function () {
    const tooltipText = Cypress.sdt.current.step.simpleValues[0];
    Cypress.sdt.current.step.target.get().realHover();
    const element = new Element({ label: "{Tooltip}" });
    const selector = element["selector"];
    cy.contains(selector, tooltipText);
  },

  /**
   * Validates the current page URL
   * @description Checks if the current URL includes the expected URL fragment
   */
  "Check Url": function () {
    let url = Cypress.sdt.current.step.simpleValues[0];
    if (!url.startsWith("http")) url = Cypress.config("baseUrl") + url;
    cy.url().should((currentUrl) => expect(currentUrl).to.include(url));
  },

  /**
   * Clears the content of an input element
   * @description Force clears the target input field
   */
  Clear: function () {
    Cypress.sdt.current.step.target.get().clear({ force: true });
  },

  /**
   * Performs click actions on various element types
   * @description Handles different click scenarios including:
   * - Force clicks for file selectors, icons, tabs, and inputs
   * - Body clicks for general page interaction
   * - Cross-origin link navigation
   * - Standard element clicks
   */
  Click: function () {
    const currentStep = Cypress.sdt.current.step;
    const targetObject = Cypress.sdt.current.step.target;
    const config = Cypress.sdt.config;

    // Define elements that require force clicking
    const forceClickElements = [
      {
        condition: () =>
          targetObject.label?.startsWith(config.recorderFileSelector),
        type: "file selector",
      },
      { condition: () => targetObject.lastElementNameIs("icon"), type: "icon" },
      { condition: () => targetObject.lastElementNameIs("tab"), type: "tab" },
      {
        condition: () => targetObject.lastElementNameIncludes("input"),
        type: "input",
      },
    ];

    // Force click for specific element types that may be obscured
    if (forceClickElements.some((elem) => elem.condition())) {
      targetObject.get().click({ force: true });
      return;
    }

    // Handle body click for general page interaction
    if (targetObject.lastElementNameIs("body")) {
      cy.get("body").click();
      return;
    }

    // Handle links with cross-origin navigation
    if (targetObject.lastElementNameIs("link")) {
      const avoidSameOrigin =
        currentStep.simpleValues[0]?.toLowerCase() ===
        "avoid same origin policy";

      if (avoidSameOrigin) {
        targetObject
          .get()
          .invoke("attr", "href")
          .then((href: string) => {
            cy.origin(href, () => cy.visit(href));
          });
        return;
      }
    }

    // Default click behavior for standard elements
    targetObject.get().click();
  },
  /**
   * Sets focus on an element, with special handling for checkboxes
   * @description Focuses the target element and validates checkbox state if applicable
   */
  Focus: function () {
    if (Cypress.sdt.current.step.target.lastElementNameIs("checkbox")) {
      Cypress.sdt.current.step.target
        .get()
        .invoke("attr", "aria-checked")
        .should("eq", "true");
    }
    Cypress.sdt.current.step.target.get().focus();
  },

  /**
   * Hovers over an element using real mouse events
   * @description Performs a realistic hover action on the target element
   */
  Hover: function () {
    Cypress.sdt.current.step.target.get().realHover();
  },

  /**
   * Simulates keyboard key presses
   * @description Handles special keys like Tab, Shift+Tab, and regular key presses
   */
  "Press Key": function () {
    const keyToType = Cypress.sdt.current.step.simpleValues[0]
      .toLowerCase()
      .trim()
      .replace(/\s*/g, "");

    // Focus the target element if specified
    if (Cypress.sdt.current.step.target.label) {
      Cypress.sdt.current.step.target.get().focus();
    }

    // Handle special key combinations
    if (keyToType.toLowerCase().trim() === "{tab}") {
      cy.focused().tab();
    } else if (keyToType.toLowerCase().trim() === "{shiftTab}") {
      cy.focused().tab({ shift: true });
    } else {
      cy.focused().type(`{${keyToType}}`);
    }
  },

  /**
   * Selects options from dropdowns or menu systems
   * @description Handles both menu navigation and dropdown option selection
   */
  Select: function () {
    // Handle hierarchical menu navigation
    if (Cypress.sdt.current.step.target.lastElementNameIs("Menu Option")) {
      const menuOptions = Cypress.sdt.current.step.simpleValues;
      menuOptions.forEach((menuOption: string, index: number) => {
        if (index === 0) {
          cy.get(`mat-sidenav a:contains(${menuOption})`).click();
        } else {
          cy.get(`button:contains(${menuOption})`).click();
        }
      });
      return;
    }

    // Handle dropdown selection
    Cypress.sdt.current.step.target.get().click();
    const element = new Element({ label: "{Not Visible Option}" });
    const selector = element["selector"];
    const option = Cypress.sdt.current.step.simpleValues[0];
    cy.get(`${selector}`)
      .filter((_, el) => {
        return Cypress.$(el).text().trim() === option;
      })
      .click();
  },
  /**
   * Sets checkbox state to checked or unchecked
   * @description Toggles checkbox state based on the desired state parameter
   */
  "Set Checkbox": function () {
    Cypress.sdt.current.step.target.get().then((checkbox: any) =>
      cy
        .wrap(checkbox)
        .invoke("prop", "checked")
        .then((checkedProp: boolean) => {
          const desiredState = Cypress.sdt.current.step.simpleValues[0]
            ?.toLowerCase()
            .trim();

          // Check the checkbox if it's unchecked and should be checked
          if (checkedProp === false && desiredState === "checked") {
            cy.wrap(checkbox).click();
          }

          // Uncheck the checkbox if it's checked and should be unchecked
          if (
            checkedProp === true &&
            (!desiredState || desiredState === "unchecked")
          ) {
            cy.wrap(checkbox).click();
          }
        })
    );
  },

  /**
   * Types text into input fields
   * @description Clears the field and types the specified value, with support for entry expansion
   */
  Type: function () {
    let valueToType = Cypress.sdt.current.step.simpleValues[0];

    // If no value provided, expand entry from element content
    if (!valueToType) {
      const entry = new Entry(
        `[${Cypress.sdt.current.step.target.lastElement.content}]`
      );
      valueToType = entry.expandEntry();
    }

    Cypress.sdt.current.step.target
      .get()
      .clear({ force: true })
      .type(valueToType);
  },
  // ****************************************************************
  // Whole Page Actions
  // ****************************************************************

  /**
   * Prevents new browser tabs from opening during tests
   * @description Stubs the window.open method to intercept tab opening attempts
   */
  "Bypass New Browser Tab": function () {
    cy.window().then((win) => {
      cy.stub(win, "open").as("OpenBrowserTab");
    });
  },

  /**
   * Prevents new browser windows from opening during tests
   * @description Stubs the window.open method and saves current URL for restoration
   */
  "Bypass New Browser Window": function () {
    cy.window().then((win) => {
      Cypress.sdt.currentWindowUrl = win.location.href;
      cy.stub(win, "open").as("OpenBrowserWindow");
    });
  },

  /**
   * Validates that a new browser tab would have been opened with the expected URL
   * @description Checks the stubbed window.open call for the correct URL pattern
   */
  "Check New Browser Tab": function () {
    const newBrowserTabUrl = Cypress.sdt.current.step.simpleValues[0];
    cy.get("@OpenBrowserTab")
      .should(
        "have.been.calledOnceWith",
        Cypress.sinon.match(new RegExp(newBrowserTabUrl))
      )
      .invoke("restore");
  },

  /**
   * Validates that a new browser window would have been opened with the expected URL
   * @description Checks the stubbed window.open call and restores the original URL
   */
  "Check New Browser Window": function () {
    const newBrowserWindowUrl = Cypress.sdt.current.step.simpleValues[0];
    cy.get("OpenBrowserWindow")
      .should(
        "have.been.calledOnceWith",
        Cypress.sinon.match(new RegExp(newBrowserWindowUrl))
      )
      .invoke("restore");
    cy.window().then((win) => {
      win.location.href = Cypress.sdt.currentWindowUrl;
    });
  },
  /**
   * Navigates browser history (back/forward)
   * @description Performs browser navigation based on direction parameter
   */
  Go: function () {
    const direction = Cypress.sdt.current.step.simpleValues[0];
    if (["back", "forward"].includes(direction.toLowerCase().trim())) {
      cy.go(direction.toLowerCase().trim() as "back" | "forward");
    }
  },

  /**
   * Navigates to a specific URL
   * @description Visits the specified URL, prepending baseUrl if needed
   */
  "Goto URL": function () {
    let url = Cypress.sdt.current.step.simpleValues[0];
    if (!url.startsWith("http")) url = `${Cypress.config("baseUrl")}/${url}`;
    cy.visit(url);
  },

  /**
   * Opens a page with optional wait time
   * @description Visits a URL and optionally waits for a specified interval
   */
  "Open Page": function () {
    let url =
      Cypress.sdt.current.step.simpleValues[0] ?? Cypress.config("baseUrl");
    const waitInterval = Cypress.sdt.current.step.simpleValues[1] ?? 2;
    if (!url.startsWith("http")) url = `${Cypress.config("baseUrl")}/${url}`;

    if (waitInterval) {
      cy.visit(url).wait(waitInterval * 1000);
    } else {
      cy.visit(url);
    }
  },

  /**
   * Scrolls within an element or the page
   * @description Scrolls to a specified position within the target element
   */
  Scroll: function () {
    let targetObject = Cypress.sdt.current.step.target;
    if (!targetObject.label) {
      targetObject = new Target("form");
    }
    const position = !Cypress.sdt.current.step.simpleValues[0]
      ? "bottom"
      : Cypress.sdt.current.step.simpleValues[0].toLowerCase();

    targetObject
      .get()
      .scrollTo(position, { ensureScrollable: false, timeout: 4000 });
  },

  /**
   * Scrolls an element into the viewport
   * @description Makes the target element visible by scrolling it into view
   */
  "Scroll Into View": function () {
    const lastElement =
      Cypress.sdt.current.step.target.elements[
        Cypress.sdt.current.step.target.elements.length - 1
      ];
    if (lastElement) {
      lastElement.isVisible = false;
      lastElement.selector = lastElement.selector.replace(":visible", "");
    }
    Cypress.sdt.current.step.target.get().scrollIntoView();
  },
  // ****************************************************************
  // Auxiliary Actions
  // ****************************************************************

  /**
   * Logs messages to the Cypress test runner
   * @description Outputs up to two messages to the Cypress command log
   */
  Log: function () {
    if (Cypress.sdt.current.step.simpleValues[0])
      cy.log(Cypress.sdt.current.step.simpleValues[0]);
    if (Cypress.sdt.current.step.simpleValues[1])
      cy.log(Cypress.sdt.current.step.simpleValues[1]);
  },

  /**
   * Logs messages to the browser console
   * @description Outputs formatted messages to the browser console for debugging
   */
  LogToConsole: function () {
    cypressHelper.clog("===================");
    cypressHelper.clog(Cypress.sdt.current.step.label);
    cypressHelper.clog("-----");
    if (Cypress.sdt.current.step.simpleValues[0])
      cypressHelper.clog(Cypress.sdt.current.step.simpleValues[0]);
    if (Cypress.sdt.current.step.simpleValues[1])
      cypressHelper.clog(Cypress.sdt.current.step.simpleValues[1]);
    cypressHelper.clog("===================");
  },

  /**
   * Pauses test execution for debugging
   * @description Stops test execution and opens the Cypress debugger
   */
  Pause: function () {
    cy.pause();
  },

  /**
   * Executes a custom Node.js task
   * @description Runs a predefined task in the Cypress Node.js environment
   */
  "Run Node Task": function () {
    const taskToRun = Cypress.sdt.current.step.simpleValues[0]
      .toLowerCase()
      .trim();
    cy.task(taskToRun);
  },

  /**
   * Sets up API request interception
   * @description Configures Cypress to intercept specific API calls
   */
  "Set Interceptor": function () {
    const interceptor =
      Cypress.sdt.apiInterceptors[Cypress.sdt.current.step.simpleValues[0]];
    cy.intercept(interceptor.httpMethod, interceptor.endPoint).as("waitForApi");
  },

  /**
   * Sets a custom date for testing time-dependent functionality
   * @description Overrides the system date with a specified date
   */
  "Set New Date": function () {
    const newDate = Cypress.sdt.current.step.simpleValues[0];
    cy.clock(new Date(newDate));
  },

  /**
   * Restores the original system date
   * @description Resets the date back to the actual system date
   */
  "Set Original Date": function () {
    window.Date = Cypress.sdt.OriginalDate;
  },

  /**
   * Waits for a specified time or for spinners to disappear
   * @description Pauses test execution with special handling for loading spinners
   */
  Wait: function () {
    // Special handling for spinner elements
    if (Cypress.sdt.current.step.target?.lastElementNameIs("spinner")) {
      const spinnerSelector =
        Cypress.sdt.current.step.target?.lastElement?.selector;
      cy.wait(1000).then(() => {
        if (Cypress.$(spinnerSelector).length > 0) {
          cy.get(spinnerSelector).should("exist");
          cy.get(spinnerSelector).should("have.length", 0);
        }
      });
    }

    // Wait for specified timeout (default 2 seconds)
    let timeout = 2000;
    if (Cypress.sdt.current.step.simpleValues[0])
      timeout = parseInt(Cypress.sdt.current.step.simpleValues[0]) * 1000;
    cy.wait(timeout);
  },

  /**
   * Waits for intercepted API calls to complete
   * @description Waits for the previously set up API interceptor and validates response
   */
  "Wait Interceptor": (_sdt: any) => {
    cy.wait("@waitForApi").its("response.statusCode").should("eq", 200);
  },
  // ****************************************************************
  // Database Actions
  // ****************************************************************

  /**
   * Drops the test database
   * @description Completely removes all data from the test database
   */
  "Drop DB": function () {
    cy.task("dbDropDatabase");
  },

  /**
   * Exports database data to JSON files
   * @description Saves current database state to a specified folder
   */
  "Export DB": function (folderName?: string) {
    folderName = folderName || Cypress.sdt.current.step?.simpleValues[0];
    const folderPath = path.join(
      Cypress.sdt.config.runFolder,
      "dbJsonFiles",
      folderName
    );
    cy.task("exportDb", folderPath);
  },

  /**
   * Loads database data from JSON files
   * @description Restores database state from a specified folder
   */
  "Set DB": function (folderName?: string) {
    folderName = folderName || Cypress.sdt.current.step?.simpleValues[0];
    const folderPath = path.join(
      Cypress.sdt.config.runFolder,
      "dbJsonFiles",
      folderName
    );
    cy.task("setDb", folderPath);
  },
  // ****************************************************************
  // Entity Actions
  // ****************************************************************

  /**
   * Validates the current active entity
   * @description Checks if the current entity matches the expected entity ID
   */
  "Check Current Entity": function () {
    const expectedCurrentEntity = Cypress.sdt.current.step.simpleValues[0];
    if (
      !expectedCurrentEntity ||
      expectedCurrentEntity.toLowerCase().trim() === "none"
    ) {
      return expect(Cypress.sdt.domain.getCurrentEntityId()).to.be.null;
    } else {
      return expect(Cypress.sdt.domain.getCurrentEntityId()).to.equal(
        expectedCurrentEntity
      );
    }
  },

  /**
   * Validates entity field values
   * @description Checks if entity field values match expected values
   */
  "Check Entity Field": function () {
    if (Cypress.sdt.current.step.target) {
      const entityFieldPath = Cypress.sdt.current.step.target;
      const entityFieldValue =
        Cypress.sdt.domain.getEntityFieldValue(entityFieldPath);
      expect(entityFieldValue).to.equal(
        Cypress.sdt.current.step.simpleValues[0]
      );
      return;
    }
    Cypress.sdt.current.step.namedValues.forEach((namedValue: any) => {
      const entityFieldPath = namedValue.left;
      const entityFieldValue =
        Cypress.sdt.domain.getEntityFieldValue(entityFieldPath);
      const value = namedValue.right;
      expect(entityFieldValue).to.equal(value);
    });
  },

  /**
   * Resets the current entity context
   * @description Clears the current entity ID from the domain context
   */
  "Reset Current Entity": function () {
    Cypress.sdt.domain.resetCurrentEntityId();
  },

  /**
   * Sets the current active entity
   * @description Updates the domain context with a new current entity ID
   */
  "Set Current Entity": function () {
    Cypress.sdt.domain.setCurrentEntity(
      Cypress.sdt.current.step.simpleValues[0]
    );
  },

  /**
   * Creates or updates entity data
   * @description Sets entity values using various input formats (objects, key-value pairs, IDs)
   */
  "Set Entity": function () {
    if (Cypress.sdt.current.step.target) {
      Cypress.sdt.domain.setEntity(
        Cypress.sdt.current.step.target,
        Cypress.sdt.current.step.simpleValues[0]
      );
      return;
    }
    Cypress.sdt.current.step.simpleValues?.forEach((value: any) => {
      if (h.isObject(value)) {
        Cypress.sdt.domain.setEntity(value["Id"], value);
        return;
      }
      if (typeof value == "string" && value.includes("=")) {
        const entityFieldPath = value.split("=")[0].trim();
        const entityFieldValue = value.split("=")[1].trim();
        Cypress.sdt.domain.setEntity(entityFieldPath, entityFieldValue);
        return;
      }
      if (typeof value == "string") {
        Cypress.sdt.domain.setEntity(value, {});
        return;
      }
    });
  },
};

/**
 * Standard validation function for element properties and attributes
 * @param $elem - jQuery element to validate
 * @description Performs validation checks on elements using simple values and named values
 */
function standardValidation($elem: any) {
  // Process simple value assertions
  Cypress.sdt.current.step.simpleValues?.forEach((value: any) => {
    checkAssertion($elem, value);
  });

  // Process named value assertions (attributes and properties)
  Cypress.sdt.current.step.namedValues?.forEach((namedValue: any) => {
    const key = namedValue.left;
    let value = namedValue.right;

    // Handle generic attribute assertions
    if (key === "attribute") {
      checkAssertion($elem, value);
      return;
    }

    // Handle specific attribute checks (attr-*)
    if (key.startsWith("attr-")) {
      const attributeName = key.replace(/attr-/, "").toLowerCase().trim();
      checkProperty($elem, attributeName, value);
      return;
    }

    // Handle property checks (prop-*)
    const propertyName = key.replace(/prop-/, "").toLowerCase().trim();
    value = value.replace(/['"]/g, "");
    if (propertyName) {
      checkProperty($elem, propertyName, value);
    }
  });
}

/**
 * Checks various assertions against an element
 * @param $elem - jQuery element to check
 * @param assertion - The assertion to validate
 * @description Handles standard element state checks and text content validation
 */
function checkAssertion($elem: any, assertion: any) {
  switch (h.normalizeString(assertion)) {
    case "exist":
    case "exists":
      return expect($elem).to.exist;
    case "hidden":
      return expect($elem).to.be.hidden;
    case "checked":
      return expect($elem).to.be.checked;
    case "unchecked":
    case "notchecked":
      return expect($elem).to.be.not.checked;
    case "empty":
      return expect($elem).to.have.value("");
    case "notempty":
      return expect($elem).to.not.have.value("");
    case "enabled":
      return expect($elem).to.satisfy(($el: any) => {
        return $el.is(":enabled") || $el.attr("aria-disabled") === "false";
      });
    case "disabled":
      return expect($elem).to.satisfy(($el: any) => {
        return $el.is(":disabled") || $el.attr("aria-disabled") === "true";
      });
    case "readonly":
      return expect($elem).to.have.attr("readonly");
    case "required":
      return expect($elem).to.have.attr("required");
    case "notrequired":
    case "optional":
      return expect($elem).to.not.have.attr("required");
    case "bold":
      return expect($elem).to.have.css("font-weight", "700");
    case "italic":
      return expect($elem).to.have.css("font-style", "italic");
    case "underline":
      return expect($elem).to.have.css("Text-decoration", "underline");
    case "blink":
      return expect($elem).to.have.css("Text-decoration", "blink");
  }

  // Handle text content assertions with icon support
  assertion = assertion.trim();
  if (Cypress.sdt.current.step.icon?.left)
    assertion = `${Cypress.sdt.current.step.icon.left.trim()} ${assertion}`;
  if (Cypress.sdt.current.step.icon?.right)
    assertion = `${assertion} ${Cypress.sdt.current.step.icon.right.trim()}`;

  let elementText = $elem[0].innerText ? $elem[0].innerText : $elem.val();
  elementText = elementText.toString().trim();

  // Check for partial or exact text match
  if (Cypress.sdt.current.step.target.isPartial)
    expect(elementText).to.include(assertion);
  else expect(elementText).to.be.equal(assertion);
}

/**
 * Checks specific properties and attributes of an element
 * @param $elem - jQuery element to check
 * @param property - The property name to check
 * @param value - The expected value
 * @description Handles specialized property checks like color, text, direction, etc.
 */
function checkProperty($elem: any, property: any, value: any) {
  // Handle color property validation
  if (property === "color") {
    let color: string;
    if (value.startsWith("rgb")) {
      color = value;
    } else {
      color = h.colorCode[value].toLowerCase().trim();
    }
    return expect($elem).to.have.css("Color", color);
  }

  // Handle text content validation with icon support
  if (property === "text") {
    if (Cypress.sdt.current.step.target.isPartial)
      return expect(($elem.val() || $elem.text()).toString().trim()).to.include(
        value
      );
    if (Cypress.sdt.current.step.icon?.left)
      value = Cypress.sdt.current.step.icon.left + value;
    if (Cypress.sdt.current.step.icon?.right)
      value = value + Cypress.sdt.current.step.icon.right;
    return expect(($elem.val() || $elem.text()).toString().trim()).to.equal(
      value
    );
  }

  // Handle flex direction validation
  if (property === "direction") {
    if (h.compareNormalizedStrings(value, "row")) {
      return expect($elem).to.have.class("flex-row");
    } else if (h.compareNormalizedStrings(value, "column")) {
      return expect($elem).to.have.class("flex-column");
    }
  }

  // Handle href attribute validation
  if (property === "href") {
    return expect($elem).to.have.attr("href", value);
  }

  // Handle collection length validation
  if (property === "items" || property === "count") {
    return expect($elem).to.have.length(value);
  }

  // Convert string boolean values to actual booleans
  if (typeof value === "string") {
    if (value.toLowerCase() === "true") {
      value = true;
    } else if (value.toLowerCase() === "false") {
      value = false;
    }
  }

  // Default property validation
  return expect($elem).to.have.property(property, value);
}
