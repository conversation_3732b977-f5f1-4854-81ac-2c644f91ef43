@echo off
setlocal enabledelayedexpansion

call :blockTitle "Script Driven Tests - Dev Environment"

call :blockTitle "Check requirements"
call :checkRequirements

call :blockTitle "Terminate all Node tasks"
tasklist /FI "IMAGENAME eq node.exe" 2>NUL | find /I /N "node.exe">NUL
if "%ERRORLEVEL%"=="0" (
    timeout /t 2>nul
    taskkill -F -IM node.exe /T
) else (
    echo No node.exe process found
)

call :blockTitle "Set configuration values"
@REM Set run folder
set RUN_FOLDER=!cd!

@REM Set config.json path
set CONFIG_PATH=!RUN_FOLDER!\config.json
if not exist "%CONFIG_PATH%" (
    call :handleError "config.json not found"
)

@REM Set organization
call :getJsonValue "organization" "riverstar"
set "ORGANIZATION=!VALUE!"

@REM Set app
call :getJsonValue "app" ""
if "!VALUE!"=="" (
    for %%F in ("%RUN_FOLDER%") do set "APP=%%~nxF"
) else (
    set "APP=!VALUE!"
)

@REM Set app folder
call :getJsonValue "appFolder" ""
if "!VALUE!"=="" (
    if exist "C:\Data\rs-projects" (
        set "APP_FOLDER=C:\Data\rs-projects\!APP!"
    ) else (
        set "APP_FOLDER=!RUN_FOLDER!\!APP!"
    )
) else (
    set "APP_FOLDER=!VALUE!"
)

@REM Set app repo
call :getJsonValue "appRepo" ""
if "!VALUE!"=="" (
    if "!ORGANIZATION!"=="riverstar" (
        if defined PAT (
            set "APP_REPO=https://!PAT!@github.com/riverstar/!APP!.git"
        ) else (
            set "APP_REPO=https://github.com/riverstar/!APP!.git"
        )
    )
) else (
    set "APP_REPO=!VALUE!"
    if defined PAT (
        set "APP_REPO=!APP_REPO:$PAT=%PAT%!"
    ) else (
        set "APP_REPO=!APP_REPO:$PAT@=!"
    )
)

@REM Set app branch
call :getJsonValue "appBranch" "develop"
set "APP_BRANCH=!VALUE!"

@REM Set DB url
call :getJsonValue "dbUrl" ""
if "!VALUE!"=="" (
    if "!ORGANIZATION!"=="riverstar" (
        set "DB_URL=mongodb://127.0.0.1:27055/inMemoryDB"
    )
) else (
    set "DB_URL=!VALUE!"
    set "DB_URL=!DB_URL:$PAT=%PAT%!"
)

@REM Set package manager
call :getJsonValue "packageManager" "npm"
set "PACKAGE_MANAGER=!VALUE!"

@REM Set app url
call :getJsonValue "appUrl" ""
if "!VALUE!"=="" (
    set "APP_URL=http://localhost:4200"
) else (
    set "APP_URL=!VALUE!"
    set "APP_URL=!APP_URL:$PAT=%PAT%!"
)

@REM Set app script
call :getJsonValue "appScript" ""
set "APP_SCRIPT=!VALUE!"

@REM Set SDT folder
call :getJsonValue "sdtFolder" ""
if "!VALUE!"=="" (
    if exist "C:\Data\rs-projects" (
        set "SDT_FOLDER=C:\Data\rs-projects\sdt"
    ) else (
        set "SDT_FOLDER=!RUN_FOLDER!\sdt"
    )
) else (
    set "SDT_FOLDER=!VALUE!"
)

@REM Set SDT repo
if defined PAT (
    set "SDT_REPO=https://!PAT!@github.com/riverstar/sdt.git"
) else (
    set "SDT_REPO=https://github.com/riverstar/sdt.git"
)

@REM Set SDT script
call :getJsonValue "sdtScript" "npx cypress open"
set "SDT_SCRIPT=!VALUE!"

@REM Configuration values
echo RUN_FOLDER: %RUN_FOLDER%
echo CONFIG_PATH: %CONFIG_PATH%
echo ORGANIZATION: %ORGANIZATION%
echo APP: %APP%
echo APP_FOLDER: %APP_FOLDER%
echo APP_REPO: %APP_REPO%
echo APP_BRANCH: %APP_BRANCH%
echo DB_URL: %DB_URL%
echo PACKAGE_MANAGER: %PACKAGE_MANAGER%
echo APP_URL: %APP_URL%
echo APP_SCRIPT: %APP_SCRIPT%
echo SDT_FOLDER: %SDT_FOLDER%
echo SDT_REPO: %SDT_REPO%
echo SDT_SCRIPT: %SDT_SCRIPT%

@REM Environment variables
call :blockTitle "Set environment variables"
set "CYPRESS_BASE_URL=!APP_URL!"
set "RSD_DB_URL=!DB_URL!"

@REM Install/update application repo
if not "!APP_REPO!"=="" (
    if not exist "!APP_FOLDER!" (
        call :blockTitle "Clone main application repo"
        git clone --single-branch --branch !APP_BRANCH! !APP_REPO! !APP_FOLDER!
        if errorlevel 1 (
            call :handleError "Failed to clone repository" "Repo: !APP_REPO!, Branch: !APP_BRANCH!"
        )
    ) else (
        call :blockTitle "Update main application"
        cd !APP_FOLDER!
        git checkout !APP_BRANCH!
        git pull origin !APP_BRANCH!
        if errorlevel 1 (
            call :handleError "Failed to pull repository" "Repo: !APP_REPO!, Branch: !APP_BRANCH!"
        )
    )

    cd !APP_FOLDER!
    git switch --detach !APP_BRANCH!
    if "!PACKAGE_MANAGER!"=="npm" (
        call :blockTitle "Install !APP! application using npm"
        if defined PAT (
          set NODE_AUTH_TOKEN=%PAT%
        )
        call npm i -D --no-save --lock=false
        if errorlevel 1 (
            call :handleError "npm install failed" "App: !APP!"
        )
    ) else (
        call :blockTitle "Install main application using yarn"
        call yarn install
        if errorlevel 1 (
            call :handleError "yarn install failed" "App: !APP!"
        )
    )
)

@REM Run application
if not "!APP_REPO!"=="" (
    call :blockTitle "Run main application"
    start /b %APP_SCRIPT%
)

@REM Wait for application to be ready
if not "!APP_REPO!"=="" (
    :wait_for_app
    curl -s %APP_URL% >nul
    if errorlevel 1 (
        ping -n 5 127.0.0.1 >nul
        goto wait_for_app
    )
)

@REM Install/update SDT repo
if not exist "!SDT_FOLDER!" (
    call :blockTitle "Clone  SDT application repo"
    call git clone --single-branch --branch main !SDT_REPO! !SDT_FOLDER!
    if errorlevel 1 (
        call :handleError "Failed to clone repository" "Repo: !SDT_REPO!, Branch: main"
    )
) else (
    call :blockTitle "Update SDT application repo"
    cd !SDT_FOLDER!
    git checkout -- package-lock.json
    git pull
    if errorlevel 1 (
        call :handleError "Failed to pull repository" "Repo: !SDT_REPO!, Branch: main"
    )
)

@REM Install SDT
call :blockTitle "Install SDT"
call npm i
if errorlevel 1 (
    call :handleError "npm install failed" "App: SDT"
)

@REM Run SDT
call :blockTitle "Run SDT"
call %SDT_SCRIPT%

@REM ================================================== Functions

:blockTitle
echo.
echo ====== %~1
echo.
exit /b

:checkRequirements
where git >nul 2>&1
if errorlevel 1 (
    call :handleError "git is not installed or not in PATH"
)
where node >nul 2>&1
if errorlevel 1 (
    call :handleError "Node.js is not installed or not in PATH"
)
where npm >nul 2>&1 || where yarn >nul 2>&1
if errorlevel 1 (
    call :handleError "Neither npm nor yarn is installed"
)
echo Requirements are met
exit /b

:getJsonValue
set "jsonKey=%~1"
set "defaultValue=%~2"
set "VALUE="
for /f "usebackq tokens=2 delims=:," %%a in (`type "%CONFIG_PATH%" ^| findstr /C:"\"%jsonKey%\""`) do (
    set "VALUE=%%a"
)
if defined VALUE (
    set "VALUE=!VALUE:~2,-1!"
) else (
    set "VALUE=%defaultValue%"
)
exit /b

:handleError
set "ERROR_MESSAGE=%~1"
set "ERROR_CONTEXT=%~2"
echo.
echo ====== ERROR ======
echo Message: %ERROR_MESSAGE%
if defined ERROR_CONTEXT echo Context: %ERROR_CONTEXT%
echo ===================
echo.
echo Press any key to exit...
pause >nul
exit /b 1
