/**
 * Extension registry - AUTO-GENERATED FILE
 * This file is automatically generated by scripts/generateExtensionRegistry.js
 * Do not edit manually - your changes will be overwritten
 */

import PROJECT_MAP from "../../projects/projectsMap";

// Type definition for extension modules
interface ExtensionModule {
  default: any;
}

/**
 * Static import mapping for all discovered extensions
 * This ensures bundler compatibility while maintaining flexibility
 */
const staticImports: Record<string, () => Promise<ExtensionModule>> = {
  "dorian/jba": () => import("../extensions/projects/dorian/jba/sdt"),
  "other/rwa": () => import("../extensions/projects/other/rwa/sdt"),
  "riverstar/billing": () => import("../extensions/projects/riverstar/billing/sdt"),
  "riverstar/ce": () => import("../extensions/projects/riverstar/ce/sdt"),
  "riverstar/pwv": () => import("../extensions/projects/riverstar/pwv/sdt"),
  "riverstar/riverstar": () => import("../extensions/projects/riverstar/riverstar/sdt"),
  "riverstar/srm": () => import("../extensions/projects/riverstar/srm/sdt"),
};

/**
 * Extension registry built from PROJECT_MAP configuration
 * Maps project names to their corresponding import functions
 */
export const extensionRegistry: Record<string, () => Promise<ExtensionModule>> = {};

// Generate the registry based on PROJECT_MAP
Object.entries(PROJECT_MAP).forEach(([projectName, config]) => {
  const { organization, project } = config;
  const importKey = `${organization}/${project}`;

  if (staticImports[importKey]) {
    extensionRegistry[projectName] = staticImports[importKey];
  } else {
    console.warn(
      `No extension found for project '${projectName}' at path '${importKey}'`
    );
  }
});

export default extensionRegistry;
