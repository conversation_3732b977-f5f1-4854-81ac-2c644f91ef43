/**
 * Extension registry that maps project names to their extension modules
 * This file is auto-generated based on the PROJECT_MAP configuration
 */

import PROJECT_MAP from "../../projects/projectsMap";

// Type definition for extension modules
interface ExtensionModule {
  default: any;
}

/**
 * Static import mapping for bundler compatibility
 * All possible extension paths are defined here with static imports
 */
const staticImports: Record<string, () => Promise<ExtensionModule>> = {
  "riverstar/ce": () => import("../extensions/projects/riverstar/ce/sdt"),
  "riverstar/billing": () =>
    import("../extensions/projects/riverstar/billing/sdt"),
  "riverstar/pwv": () => import("../extensions/projects/riverstar/pwv/sdt"),
  "riverstar/riverstar": () =>
    import("../extensions/projects/riverstar/riverstar/sdt"),
  "riverstar/srm": () => import("../extensions/projects/riverstar/srm/sdt"),
  "dorian/jba": () => import("../extensions/projects/dorian/jba/sdt"),
  "other/rwa": () => import("../extensions/projects/other/rwa/sdt"),
};

/**
 * Extension registry built from PROJECT_MAP configuration
 * Maps project names to their corresponding import functions
 */
export const extensionRegistry: Record<string, () => Promise<ExtensionModule>> =
  {};

// Generate the registry based on PROJECT_MAP
Object.entries(PROJECT_MAP).forEach(([projectName, config]) => {
  const { organization, project } = config;
  const importKey = `${organization}/${project}`;

  if (staticImports[importKey]) {
    extensionRegistry[projectName] = staticImports[importKey];
  } else {
    console.warn(
      `No static import found for project '${projectName}' (${importKey})`
    );
  }
});

export default extensionRegistry;
