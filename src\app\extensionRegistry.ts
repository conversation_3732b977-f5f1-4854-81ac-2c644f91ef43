/**
 * Extension registry that maps project names to their extension modules
 * This file is auto-generated based on the PROJECT_MAP configuration
 */

import PROJECT_MAP from "../../projects/projectsMap";

// Type definition for extension modules
interface ExtensionModule {
  default: any;
}

/**
 * Static registry of all available extensions for bundler compatibility
 * This registry is built from the PROJECT_MAP to ensure all configured
 * projects have their extensions available for dynamic loading
 */
export const extensionRegistry: Record<
  string,
  () => Promise<ExtensionModule>
> = {};

// Generate the registry based on PROJECT_MAP
Object.entries(PROJECT_MAP).forEach(([projectName, config]) => {
  const { organization, project } = config;
  
  // Create static import statements for each project
  // Note: These need to be explicit for bundler analysis
  switch (`${organization}/${project}`) {
    case "riverstar/ce":
      extensionRegistry[projectName] = () => import("../extensions/projects/riverstar/ce/sdt");
      break;
    case "riverstar/billing":
      extensionRegistry[projectName] = () => import("../extensions/projects/riverstar/billing/sdt");
      break;
    case "riverstar/pwv":
      extensionRegistry[projectName] = () => import("../extensions/projects/riverstar/pwv/sdt");
      break;
    case "riverstar/riverstar":
      extensionRegistry[projectName] = () => import("../extensions/projects/riverstar/riverstar/sdt");
      break;
    case "riverstar/srm":
      extensionRegistry[projectName] = () => import("../extensions/projects/riverstar/srm/sdt");
      break;
    case "dorian/jba":
      extensionRegistry[projectName] = () => import("../extensions/projects/dorian/jba/sdt");
      break;
    case "other/rwa":
      extensionRegistry[projectName] = () => import("../extensions/projects/other/rwa/sdt");
      break;
    default:
      console.warn(`No extension registry entry found for project '${projectName}' (${organization}/${project})`);
  }
});

export default extensionRegistry;
