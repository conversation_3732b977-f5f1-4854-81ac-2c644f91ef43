# Configuration Guide

This guide explains all configuration options available in the SDT framework, including environment variables, configuration files, and Cypress settings.

## Overview

SDT uses multiple configuration layers to provide flexibility for different environments and projects:

1. **Environment Variables** - Runtime configuration
2. **Project Configuration Files** - Project-specific settings
3. **Cypress Configuration** - Test runner settings
4. **Extension Configuration** - Application-specific settings

## Environment Variables

### Required Variables

#### RUN_FOLDER
- **Description**: Project execution directory
- **Example**: `C:\projects\myapp`
- **Usage**: Set by run scripts to specify the working directory

#### CONFIG_PATH
- **Description**: Path to the project configuration JSON file
- **Example**: `C:\projects\myapp\config.json`
- **Usage**: Points to the main configuration file for the project

#### APP
- **Description**: Application name for extension loading
- **Example**: `riverstar`, `billing`, `pwv`
- **Usage**: Determines which extension to load

### Optional Variables

#### RSD_DB_URL
- **Description**: MongoDB connection string
- **Example**: `mongodb://127.0.0.1:27055/testDB`
- **Usage**: Database connection for data-driven testing
- **Default**: None (database features disabled)

#### CYPRESS_BASE_URL
- **Description**: Base URL for the application under test
- **Example**: `http://localhost:4200`
- **Usage**: Automatically set from appUrl in config file

#### PAT
- **Description**: Personal Access Token for private repositories
- **Example**: `ghp_xxxxxxxxxxxxxxxxxxxx`
- **Usage**: Used for cloning private repositories

## Project Configuration Files

### Basic Configuration

Create a `config.json` file in your project directory:

```json
{
  "organization": "your-organization",
  "app": "your-app-name",
  "appUrl": "http://localhost:4200",
  "sdtFile": "your-tests.xlsx"
}
```

### Complete Configuration Options

```json
{
  "organization": "organization-name",
  "app": "application-name",
  "appRepo": "https://github.com/user/app.git",
  "appBranch": "develop",
  "appUrl": "http://localhost:4200",
  "appScript": "npm start",
  "appFolder": "../app",
  "packageManager": "npm",
  "sdtFile": "tests.xlsx",
  "sdtScript": "npx cypress open",
  "dbUrl": "mongodb://127.0.0.1:27055/testDB",
  "takeScreenshotOnError": "yes",
  "takeScreenshotOnTitle": "no",
  "resultsFolderName": "results",
  "resultsFolderPath": "./results"
}
```

### Configuration Properties

#### Core Settings

| Property | Required | Description | Default |
|----------|----------|-------------|---------|
| `organization` | Yes | Organization name | - |
| `app` | Yes | Application identifier | - |
| `appUrl` | Yes | Application URL | - |
| `sdtFile` | Yes | Excel test file name | `{app}.xlsx` |

#### Application Settings

| Property | Required | Description | Default |
|----------|----------|-------------|---------|
| `appRepo` | No | Git repository URL | Auto-generated |
| `appBranch` | No | Git branch to use | `develop` |
| `appScript` | No | Command to start app | `npm start` |
| `appFolder` | No | App directory path | `../{app}` |
| `packageManager` | No | Package manager | `npm` |

#### SDT Settings

| Property | Required | Description | Default |
|----------|----------|-------------|---------|
| `sdtScript` | No | SDT execution command | `npx cypress open` |
| `resultsFolderName` | No | Results folder name | `results` |
| `resultsFolderPath` | No | Results folder path | `./results` |

#### Database Settings

| Property | Required | Description | Default |
|----------|----------|-------------|---------|
| `dbUrl` | No | MongoDB connection string | None |

#### Screenshot Settings

| Property | Required | Description | Default |
|----------|----------|-------------|---------|
| `takeScreenshotOnError` | No | Screenshot on errors | `yes` |
| `takeScreenshotOnTitle` | No | Screenshot on titles | `no` |

### Environment-Specific Configurations

#### Development Configuration
```json
{
  "organization": "myorg",
  "app": "myapp",
  "appUrl": "http://localhost:3000",
  "appScript": "npm run dev",
  "sdtFile": "dev-tests.xlsx",
  "takeScreenshotOnError": "yes",
  "takeScreenshotOnTitle": "yes"
}
```

#### Production Configuration
```json
{
  "organization": "myorg",
  "app": "myapp",
  "appUrl": "https://myapp.production.com",
  "appScript": "npm start",
  "sdtFile": "prod-tests.xlsx",
  "takeScreenshotOnError": "yes",
  "takeScreenshotOnTitle": "no"
}
```

## Cypress Configuration

### Default Cypress Settings

The framework uses these default Cypress settings in `cypress.config.ts`:

```typescript
export default defineConfig({
  e2e: {
    numTestsKeptInMemory: 1,
    fileServerFolder: "./",
    supportFile: "./src/app/e2e.ts",
    specPattern: "./src/app/runSdt.ts",
    trashAssetsBeforeRuns: true,
    video: true,
    chromeWebSecurity: false,
    baseUrl: "http://localhost:4200",
    viewportWidth: 1920,
    viewportHeight: 1080,
    defaultCommandTimeout: 10000,
    pageLoadTimeout: 10000,
    retries: {
      runMode: 1,
      openMode: 0
    }
  }
});
```

### Customizing Cypress Settings

#### Viewport Configuration
```typescript
viewportWidth: 1366,
viewportHeight: 768
```

#### Timeout Configuration
```typescript
defaultCommandTimeout: 15000,
pageLoadTimeout: 30000,
requestTimeout: 10000
```

#### Retry Configuration
```typescript
retries: {
  runMode: 2,    // Retry failed tests 2 times in headless mode
  openMode: 0    // No retries in interactive mode
}
```

#### Browser Configuration
```typescript
chromeWebSecurity: false,  // Disable for cross-origin testing
userAgent: "Custom User Agent String"
```

## Extension Configuration

### Organization-Level Configuration

Extensions can inherit from organization-level configurations:

```typescript
// organizations/myorg/core/config.ts
export default {
  admin: {
    username: "admin",
    password: "admin123",
    email: "<EMAIL>"
  },
  consoleLog: true,
  apiTimeout: 30000
};
```

### Project-Level Configuration

Projects can override organization settings:

```typescript
// projects/myorg/myapp/extension/config.ts
export default {
  ...organizationConfig,
  apiEndpoint: "https://api.myapp.com",
  features: {
    enableAdvancedReporting: true,
    enableApiMocking: false
  }
};
```

## Path Configuration

### TypeScript Path Mapping

The framework uses TypeScript path mapping for clean imports:

```json
{
  "paths": {
    "@/*": ["./src/*"],
    "@organizations/*": ["./organizations/*"]
  }
}
```

### Usage Examples
```typescript
import helpers from "@/app/helpers/all";
import orgConfig from "@organizations/myorg/config";
```

## Database Configuration

### MongoDB Connection

#### Local Development
```json
{
  "dbUrl": "mongodb://127.0.0.1:27017/testdb"
}
```

#### Docker Environment
```json
{
  "dbUrl": "mongodb://mongo:27017/testdb"
}
```

#### Cloud Database
```json
{
  "dbUrl": "mongodb+srv://user:<EMAIL>/testdb"
}
```

### Database Tasks

Configure database operations in extension tasks:

```typescript
export default {
  setupTestData: async () => {
    await global.db.collection('users').deleteMany({});
    await global.db.collection('users').insertMany(testUsers);
  }
};
```

## Security Configuration

### Authentication Settings

```json
{
  "auth": {
    "type": "basic",
    "username": "testuser",
    "password": "testpass"
  }
}
```

### API Security

```json
{
  "api": {
    "baseUrl": "https://api.example.com",
    "timeout": 30000,
    "headers": {
      "Authorization": "Bearer ${API_TOKEN}",
      "Content-Type": "application/json"
    }
  }
}
```

## Performance Configuration

### Memory Settings

```typescript
// Cypress configuration
numTestsKeptInMemory: 1,  // Reduce memory usage
```

### Parallel Execution

```json
{
  "parallel": {
    "enabled": true,
    "workers": 4
  }
}
```

## Debugging Configuration

### Development Mode

```json
{
  "debug": {
    "enabled": true,
    "logLevel": "verbose",
    "pauseOnError": true
  }
}
```

### Logging Configuration

```json
{
  "logging": {
    "console": true,
    "file": "./logs/sdt.log",
    "level": "info"
  }
}
```

## Best Practices

### Configuration Management

1. **Environment Separation**: Use different configs for dev/test/prod
2. **Secret Management**: Keep sensitive data in environment variables
3. **Version Control**: Don't commit sensitive configuration files
4. **Documentation**: Document all configuration options

### File Organization

```
project/
├── config.json              # Main configuration
├── config.dev.json         # Development overrides
├── config.prod.json        # Production overrides
└── .env                    # Environment variables
```

### Configuration Validation

Validate configuration at startup:

```typescript
function validateConfig(config: any) {
  const required = ['organization', 'app', 'appUrl', 'sdtFile'];
  for (const field of required) {
    if (!config[field]) {
      throw new Error(`Required configuration field missing: ${field}`);
    }
  }
}
```

## Troubleshooting

### Common Configuration Issues

#### Missing Environment Variables
- **Error**: "Required environment variables RUN_FOLDER or CONFIG_PATH are missing"
- **Solution**: Ensure run scripts set these variables correctly

#### Invalid JSON Configuration
- **Error**: JSON parsing errors
- **Solution**: Validate JSON syntax in configuration files

#### Path Resolution Issues
- **Error**: Cannot find files or modules
- **Solution**: Check path mappings and file locations

#### Database Connection Issues
- **Error**: MongoDB connection failures
- **Solution**: Verify database URL and network connectivity

### Configuration Debugging

Enable debug logging to troubleshoot configuration issues:

```json
{
  "debug": true,
  "consoleLog": true
}
```

Use environment variable debugging:

```bash
set DEBUG=sdt:*
npm run sdt:gui
```
