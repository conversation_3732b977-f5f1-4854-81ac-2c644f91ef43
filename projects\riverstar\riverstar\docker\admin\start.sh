#!/bin/bash

echo
echo "======> Environment variables"
echo
echo RUN_FOLDER: $RUN_FOLDER
echo CONFIG_PATH: $CONFIG_PATH
echo ORGANIZATION: $ORGANIZATION
echo APP: $APP
echo APP_FOLDER: $APP_FOLDER
echo APP_REPO: $APP_REPO
echo PACKAGE_MANAGER: $PACKAGE_MANAGER
echo APP_URL: $APP_URL
echo APP_SCRIPT: $APP_SCRIPT
echo SDT_FOLDER: $SDT_FOLDER
echo SDT_SCRIPT: $SDT_SCRIPT
echo CYPRESS_BASE_URL: $CYPRESS_BASE_URL
echo RSD_DB_URL: $RSD_DB_URL

read -p "Press Enter to continue..."

if [ -n "$APP_REPO" ]; then
  echo
  echo "======> Update main application repo"
  echo
  cd $APP_FOLDER
  git pull

  if [ "$PACKAGE_MANAGER" = "npm" ]; then
    echo
    echo ====== Install main application using npm
    echo
    npm i -D --no-save --lock=false
  else
    echo
    echo ====== Install main application using yarn
    echo
    /root/.yarn/bin/yarn install
  fi

  if [ "$PACKAGE_MANAGER" = "npm" ]; then
    echo
    echo ====== Run main application using npm
    echo
    eval "$(echo "$APP_SCRIPT")" &
  else
    echo
    echo ====== Run main application using yarn
    echo
    eval "$(echo "$APP_SCRIPT")" &
    /root/.yarn/bin/yarn run $APP_SCRIPT
  fi
fi

echo
echo "======> Update SDT application repo"
echo
cd $SDT_FOLDER
git checkout -- package-lock.json
git pull

echo
echo "======> Update extension folder"
echo
rm -rf $SDT_FOLDER/src/app/extension
cp -r $RUN_FOLDER/extension $SDT_FOLDER/src/app/extension

echo
echo "======> Install SDT"
echo
npm i

if [ ! -z "$APP_REPO" ]; then
  while ! curl -s $APP_URL >/dev/null; do sleep 5; done
fi
echo
echo "======> Run SDT"
echo
eval "$(echo "$SDT_SCRIPT")"
