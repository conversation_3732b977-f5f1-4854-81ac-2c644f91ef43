/**
 * Task loader module for dynamically importing project-specific tasks
 *
 * This module provides a centralized way to load task definitions from
 * project extensions based on the APP environment variable. It supports
 * multiple project organizations and provides comprehensive error handling.
 *
 * If a project does not have a tasks.ts file in its extension folder,
 * the module will gracefully return an empty task object and log a warning.
 */

import { IProjectConfig, PROJECT_MAP } from "../../projects/projectsMap";

// Type definitions for better type safety
interface ITaskModule {
  default: Record<string, (...args: any[]) => any>;
}

/**
 * Loads tasks for the specified project
 * @param projectName - The name of the project to load tasks for
 * @returns Promise resolving to the project's task definitions, or empty object if no tasks file exists
 * @throws Error if project is not found or tasks fail to load (excluding missing tasks file)
 */
async function loadProjectTasks(
  projectName: string
): Promise<Record<string, (...args: any[]) => any>> {
  if (!projectName) {
    throw new Error(
      "Project name is required. Please set the APP environment variable."
    );
  }

  const projectConfig = PROJECT_MAP[projectName];
  if (!projectConfig) {
    const availableProjects = Object.keys(PROJECT_MAP).join(", ");
    throw new Error(
      `Project '${projectName}' not found. Available projects: ${availableProjects}`
    );
  }

  const { organization, project } = projectConfig;
  const tasksPath = `../extensions/projects/${organization}/${project}/tasks`;

  try {
    const taskModule: ITaskModule = await import(tasksPath);

    if (!taskModule.default) {
      throw new Error(
        `Tasks module for project '${projectName}' does not export a default object`
      );
    }

    // Validate that the exported object contains callable functions
    const tasks = taskModule.default;
    const taskNames = Object.keys(tasks);

    if (taskNames.length === 0) {
      // No tasks found - this is acceptable
    }

    return tasks;
  } catch (error) {
    // Check if the error is due to module not found (tasks.ts doesn't exist)
    if (
      error instanceof Error &&
      (error.message.includes("Cannot resolve module") ||
        error.message.includes("Cannot find module") ||
        error.message.includes("Module not found") ||
        error.message.includes("ENOENT") ||
        (error as any).code === "MODULE_NOT_FOUND" ||
        (error as any).code === "ENOENT")
    ) {
      return {}; // Return empty object when tasks file doesn't exist
    }

    // Enhanced error message with more context for other errors
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    throw new Error(
      `Failed to load tasks for project '${projectName}' from '${tasksPath}': ${errorMessage}`
    );
  }
}

/**
 * Main export: Immediately invoked async function that loads tasks
 * based on the APP environment variable
 */
export default (async () => {
  try {
    const projectName = process.env.APP;
    return await loadProjectTasks(projectName);
  } catch (error) {
    // Log error for debugging while still throwing for proper error propagation
    console.error(
      "Task loading failed:",
      error instanceof Error ? error.message : error
    );
    throw error;
  }
})();
