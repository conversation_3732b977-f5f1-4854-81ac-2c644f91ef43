import fs from "fs-extra";
import path from "path";
import riverstarTasks from "@organizations/riverstar/tasks";

export default {
  ...riverstarTasks,

  dbSetCollections: async () => {
    let data;

    const agenciesFilePath = path.resolve(
      __dirname,
      "../dbJsonFiles/ce-uat.agencies.json"
    );
    data = fs.readFileSync(agenciesFilePath);
    let agencies = JSON.parse(data.toString());
    agencies = agencies.map((agency) => {
      delete agency._id;
      delete agency.createdAt;
      delete agency.updatedAt;
      return agency;
    });
    await global.db.collection("agencies").deleteMany({});
    await global.db.collection("agencies").insertMany(agencies);

    const profilesFilePath = path.join(
      __dirname,
      "../dbJsonFiles/ce-uat.profiles.json"
    );
    data = fs.readFileSync(profilesFilePath);
    let profiles = JSON.parse(data.toString());
    profiles = profiles.map((profile) => {
      delete profile._id;
      delete profile.createdAt;
      delete profile.updatedAt;
      return profile;
    });
    await global.db.collection("profiles").deleteMany({});
    await global.db.collection("profiles").insertMany(profiles);

    const templatesFilePath = path.join(
      __dirname,
      "../dbJsonFiles/ce-uat.templateAdmin.json"
    );
    data = fs.readFileSync(templatesFilePath);
    let templates = JSON.parse(data.toString());
    templates = templates.map((template) => {
      delete template._id;
      delete template.createdAt;
      delete template.updatedAt;
      return template;
    });
    await global.db.collection("templateAdmin").deleteMany({});
    await global.db.collection("templateAdmin").insertMany(templates);

    return null;
  },
};
