import User from "@organizations/riverstar/domain/user";

export default class srmUser extends User {
  agencyId = "";

  constructor(user) {
    super(user);
  }

  create() {
    return super
      .create()
      .then(() => {
        this.agencyId = Cypress.sdt.domain.data.agency.id;
      })
      .then(() =>
        Cypress.sdt.apiHandler.login(
          Cypress.sdt.config.admin.username,
          Cypress.sdt.config.admin.password
        )
      )
      .then(() => Cypress.sdt.apiHandler.setUserAgency(this.id, this.agencyId));
  }
}
