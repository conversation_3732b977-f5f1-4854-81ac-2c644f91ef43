# Test Definition Guide

This guide explains how to create and structure tests in Excel files for the SDT (Script Driven Tests) framework.

## Overview

SDT uses Excel files to define tests, making it accessible to non-programmers while providing powerful automation capabilities. Each Excel file contains multiple sheets that define different aspects of your test suite.

## Excel File Structure

### Required Sheets

Your Excel file must contain the following sheets:

1. **Tests** - Define your test cases
2. **Scripts** - Define reusable script components
3. **Tables** - Define test data tables
4. **Elements** - Define UI element selectors

### Optional Sheets

- **Aliases** - Define text aliases for reusable content

## Tests Sheet

The Tests sheet defines your test cases with the following columns:

| Column | Required | Description |
|--------|----------|-------------|
| Run | Yes | Execution flag: `x` = run, `z` = run and stop, empty = skip |
| Test | Yes | Test identifier/number |
| Use Case | No | Descriptive name for the test case |
| Action | Yes | The action to perform |
| Target | No | The target element or identifier |
| Values | No | Parameters or values for the action |
| Do | Yes | Step execution flag: `x` = execute, empty = skip |

### Example Tests Sheet

| Run | Test | Use Case | Action | Target | Values | Do |
|-----|------|----------|--------|--------|--------|-----|
| x | 1 | Login Test | Start Test | | | x |
| | | | Type | Username | admin | x |
| | | | Type | Password | password | x |
| | | | Click | Login Button | | x |
| | | | End Test | | | x |

### Test Structure

- Each test starts with `Start Test` action
- Each test ends with `End Test` action
- Tests can call scripts using action names that match script names
- Use the `Run` column to control which tests execute
- Use the `Do` column to control which steps within a test execute

## Scripts Sheet

The Scripts sheet defines reusable components with the following columns:

| Column | Required | Description |
|--------|----------|-------------|
| Script | Yes | Script name/identifier |
| Action | Yes | The action to perform |
| Target | No | The target element or identifier |
| Values | No | Parameters or values for the action |
| Do | Yes | Step execution flag: `x` = execute, empty = skip |

### Example Scripts Sheet

| Script | Action | Target | Values | Do |
|--------|--------|--------|--------|-----|
| Login | Start Script | | | x |
| | Type | Username | admin | x |
| | Type | Password | password | x |
| | Click | Login Button | | x |
| | End Script | | | x |

### Script Features

- Scripts start with `Start Script` and end with `End Script`
- Scripts can be called from tests by using the script name as an action
- Scripts support input parameters for dynamic behavior
- Scripts can call other scripts (nested execution)

## Tables Sheet

The Tables sheet defines data tables for data-driven testing:

| Column | Description |
|--------|-------------|
| Id | Unique identifier for each row |
| Field1, Field2, etc. | Data columns with your test data |

### Example Tables Sheet

| Id | Username | Password | Email |
|----|----------|----------|-------|
| User1 | john.doe | pass123 | <EMAIL> |
| User2 | jane.smith | pass456 | <EMAIL> |

### Using Table Data

- Reference table data using bracket notation: `[TableName][RowId][FieldName]`
- Example: `[Users][User1][Username]` returns "john.doe"
- Tables support cross-references between rows

## Elements Sheet

The Elements sheet defines UI element selectors:

| Column | Required | Description |
|--------|----------|-------------|
| Item | Yes | Element name/identifier |
| Selector | Yes | CSS selector or XPath |
| Root | No | Root element selector |
| Locator | No | Additional locator information |

### Example Elements Sheet

| Item | Selector | Root | Locator |
|------|----------|------|---------|
| Username | #username | | |
| Password | input[type="password"] | | |
| Login Button | .login-btn | | |
| User Menu | .user-menu | header | |

### Element Usage

- Reference elements by name in the Target column
- Elements support hierarchical selection with Root selectors
- Use descriptive names for better test readability

## Aliases Sheet (Optional)

The Aliases sheet defines reusable text content:

| Column | Description |
|--------|-------------|
| Alias Name | The alias identifier (use «alias» format) |
| Content | The text content to substitute |

### Example Aliases Sheet

| Alias Name | Content |
|------------|---------|
| «admin-email» | <EMAIL> |
| «test-message» | This is a test message |

## Advanced Features

### Functions

Use bracket notation to call built-in functions:

- `[Date(1, d)]` - Tomorrow's date
- `[Date(-7, d, "yyyy-mm-dd")]` - Last week in specific format
- `[Random(1000, 9999)]` - Random number between 1000-9999

### Value References

- Table references: `[TableName][RowId][FieldName]`
- Alias references: `«alias-name»`
- Function calls: `[FunctionName(params)]`

### Execution Control

- Use `Run` column values:
  - `x` = Execute this test
  - `z` = Execute this test and stop execution
  - Empty = Skip this test

- Use `Do` column values:
  - `x` = Execute this step
  - Empty = Skip this step

## Best Practices

1. **Naming Conventions**
   - Use descriptive test names
   - Use consistent element naming
   - Group related tests logically

2. **Test Organization**
   - Keep tests focused and atomic
   - Use scripts for common workflows
   - Organize data in logical tables

3. **Maintainability**
   - Use elements sheet for all selectors
   - Leverage scripts for reusability
   - Document complex test scenarios

4. **Data Management**
   - Use tables for test data
   - Keep data separate from test logic
   - Use aliases for repeated text

## Common Actions

### Navigation Actions
- `Navigate` - Go to a URL
- `Click` - Click an element
- `Back` - Browser back button

### Input Actions
- `Type` - Enter text in a field
- `Clear` - Clear a field
- `Select` - Select from dropdown

### Validation Actions
- `Check` - Verify element properties
- `Wait` - Wait for conditions

### Test Control
- `Start Test` - Begin test execution
- `End Test` - Complete test execution
- `Start Script` - Begin script execution
- `End Script` - Complete script execution

For a complete list of available actions, see the [API Reference](api-reference.md).
