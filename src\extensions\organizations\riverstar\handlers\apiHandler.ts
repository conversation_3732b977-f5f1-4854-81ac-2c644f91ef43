import cypressHelper from "@/app/helpers/cypress";

interface ApiOptions {
  method: string;
  url: string;
  body?: any;
}

// Authentication related API calls
const authApi = {
  login(username: string, password: string) {
    const options: ApiOptions = {
      method: "POST",
      url: "app-api/auth/login",
      body: { username, password },
    };
    return cypressHelper.apiCall(options);
  },

  logout() {
    const options: ApiOptions = {
      method: "GET",
      url: "app-api/auth/logoutRedirect",
    };
    return cypressHelper.apiCall(options);
  },

  resetPassword(password: string) {
    const options: ApiOptions = {
      method: "PUT",
      url: "app-api/auth/reset-password",
      body: { 
        password,
        passwordMatch: null 
      },
    };
    return cypressHelper
      .apiCall(options)
      .then((response) => response["rsStore"]);
  },

  adminResetPassword(user) {
    const options: ApiOptions = {
      method: "PUT",
      url: `app-api/authusers/${user.id}/admin-reset-password`,
    };
    return cypressHelper
      .apiCall(options)
      .then((response) => user.tempPassword = response.body['tempPassword']);
  },
};

// Configuration related API calls
const configApi = {
  getAppConfig() {
    const options: ApiOptions = {
      method: "GET",
      url: "app-api/config/app-config",
    };
    return cypressHelper.apiCall(options).then((response) => response["body"]);
  },
};

// User management related API calls
const userApi = {
  createUser(user) {
    const requestBody = { ...user };
    delete requestBody.password;

    const options: ApiOptions = {
      method: "POST",
      url: "app-api/authusers",
      body: {
        rsStore: requestBody,
        sendEmail: true,
      },
    };
    return cypressHelper
      .apiCall(options)
      .then((response) => response["body"].rsStore);
  },
};

export default {
  ...authApi,
  ...configApi,
  ...userApi,
};
