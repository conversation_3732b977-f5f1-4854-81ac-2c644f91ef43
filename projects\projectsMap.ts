/**
 * Project configuration map
 * 
 * This file contains the mapping of project names to their organizational structure.
 * It allows for flexible project organization across different organizations.
 */

export interface IProjectConfig {
  organization: string;
  project: string;
}

/**
 * Map of available projects with their organizational structure
 * This allows for flexible project organization beyond just 'riverstar'
 */
export const PROJECT_MAP: Record<string, IProjectConfig> = {
  // Riverstar projects
  ce: { organization: "riverstar", project: "ce" },
  billing: { organization: "riverstar", project: "billing" },
  pwv: { organization: "riverstar", project: "pwv" },
  riverstar: { organization: "riverstar", project: "riverstar" },
  srm: { organization: "riverstar", project: "srm" },

  // Dorian Solutions projects
  jba: { organization: "dorian", project: "jba" },

  // Other projects
  rwa: { organization: "other", project: "rwa" },
};

export default PROJECT_MAP;
