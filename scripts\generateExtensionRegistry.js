#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to automatically generate the extension registry file
 * This scans the extensions directory and creates static imports for all found extensions
 */

const fs = require('fs');
const path = require('path');

// Configuration
const EXTENSIONS_DIR = path.join(__dirname, '../src/extensions/projects');
const OUTPUT_FILE = path.join(__dirname, '../src/app/extensionRegistry.ts');
const PROJECT_MAP_FILE = path.join(__dirname, '../projects/projectsMap.ts');

/**
 * Recursively scan directory for sdt.ts files
 */
function scanExtensions(dir, basePath = '') {
  const extensions = [];
  
  if (!fs.existsSync(dir)) {
    console.warn(`Extensions directory not found: ${dir}`);
    return extensions;
  }

  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const relativePath = basePath ? `${basePath}/${item}` : item;
    
    if (fs.statSync(fullPath).isDirectory()) {
      // Recursively scan subdirectories
      extensions.push(...scanExtensions(fullPath, relativePath));
    } else if (item === 'sdt.ts') {
      // Found an extension file
      const pathParts = basePath.split('/');
      if (pathParts.length >= 2) {
        const [organization, project] = pathParts;
        extensions.push({
          organization,
          project,
          importPath: `../extensions/projects/${basePath}/sdt`,
          key: `${organization}/${project}`
        });
      }
    }
  }
  
  return extensions;
}

/**
 * Generate the extension registry TypeScript file
 */
function generateRegistryFile(extensions) {
  const imports = extensions
    .map(ext => `  "${ext.key}": () => import("${ext.importPath}"),`)
    .join('\n');

  const content = `/**
 * Extension registry - AUTO-GENERATED FILE
 * This file is automatically generated by scripts/generateExtensionRegistry.js
 * Do not edit manually - your changes will be overwritten
 */

import PROJECT_MAP from "../../projects/projectsMap";

// Type definition for extension modules
interface ExtensionModule {
  default: any;
}

/**
 * Static import mapping for all discovered extensions
 * This ensures bundler compatibility while maintaining flexibility
 */
const staticImports: Record<string, () => Promise<ExtensionModule>> = {
${imports}
};

/**
 * Extension registry built from PROJECT_MAP configuration
 * Maps project names to their corresponding import functions
 */
export const extensionRegistry: Record<string, () => Promise<ExtensionModule>> = {};

// Generate the registry based on PROJECT_MAP
Object.entries(PROJECT_MAP).forEach(([projectName, config]) => {
  const { organization, project } = config;
  const importKey = \`\${organization}/\${project}\`;

  if (staticImports[importKey]) {
    extensionRegistry[projectName] = staticImports[importKey];
  } else {
    console.warn(
      \`No extension found for project '\${projectName}' at path '\${importKey}'\`
    );
  }
});

export default extensionRegistry;
`;

  return content;
}

/**
 * Main execution
 */
function main() {
  console.log('🔍 Scanning for extensions...');
  
  const extensions = scanExtensions(EXTENSIONS_DIR);
  
  console.log(`📦 Found ${extensions.length} extensions:`);
  extensions.forEach(ext => {
    console.log(`   - ${ext.key} -> ${ext.importPath}`);
  });
  
  console.log('📝 Generating extension registry...');
  
  const registryContent = generateRegistryFile(extensions);
  
  // Ensure output directory exists
  const outputDir = path.dirname(OUTPUT_FILE);
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  // Write the generated file
  fs.writeFileSync(OUTPUT_FILE, registryContent, 'utf8');
  
  console.log(`✅ Extension registry generated: ${OUTPUT_FILE}`);
  console.log('🎉 Done!');
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { scanExtensions, generateRegistryFile };
