import Domain from "@/app/core/domain";
import { ITestData } from "@/app/core/test";
import Report from "@/app/core/report";
import Test from "@/app/core/test";
import coreActions from "@/app/core/actions";
import coreFunctions from "@/app/core/functions";

// Type definitions for better type safety
interface ExtensionModule {
  default: ISdtExtension | any; // Allow for different extension patterns
}

interface ISdtExtension {
  setup?: {
    before?: () => void;
    beforeEach?: () => void;
    afterEach?: () => void;
    after?: () => void;
  };
  config?: Record<string, any>;
  icons?: Record<string, any>;
  actions?: Record<string, any>; // More flexible to handle mixed types
  functions?: Record<string, any>;
  apiHandler?: Record<string, any>;
  spyHandler?: Record<string, any>;
  apiInterceptors?: Record<string, any>;
}

interface ISdtData {
  tests: ITestData[];
  scripts: any[];
  tables: Record<string, any>;
  aliases: Record<string, any>;
  elements: Record<string, any>;
}

interface SdtConfig {
  sdtData?: ISdtData; // Make optional since it's set during initialization
  [key: string]: any;
}

interface CurrentState {
  sheet: string;
  test: Record<string, any>;
  step: Record<string, any>;
  stepIndex: number;
}

interface TestResults {
  executedTests: any[];
  testsWithError: any[];
  notCompletedTests: any[];
  omittedTests: any[];
  omittedSteps: any[];
  usedAliases: any[];
  usedElements: any[];
}

/**
 * Main SDT class that manages test execution,
 * extension loading, and test orchestration for Cypress-based testing framework.
 */
export default class Sdt {
  /** Map of available extensions with their dynamic import functions */
  private static readonly extensionMap: Record<
    string,
    () => Promise<ExtensionModule>
  > = {
    ce: () => import("../extensions/projects/riverstar/ce/sdt"),
    billing: () => import("../extensions/projects/riverstar/billing/sdt"),
    jba: () => import("../extensions/projects/dorian/jba/sdt"),
    pwv: () => import("../extensions/projects/riverstar/pwv/sdt"),
    riverstar: () => import("../extensions/projects/riverstar/riverstar/sdt"),
    rwa: () => import("../extensions/projects/other/rwa/sdt"),
    srm: () => import("../extensions/projects/riverstar/srm/sdt"),
  };

  // Extension and configuration properties
  extension?: ISdtExtension;
  config: SdtConfig;
  setup?: ISdtExtension["setup"];
  icons?: Record<string, any>;
  actions: Record<string, any>; // Allow mixed types (functions and other values)
  functions: Record<string, any>;
  apiHandler?: Record<string, any>;
  spyHandler?: Record<string, any>;
  apiInterceptors?: Record<string, any>;

  // Test data and execution state
  data: ISdtData = {
    tests: [],
    scripts: [],
    tables: [],
    aliases: {},
    elements: {},
  };
  testsToRun: ITestData[] = [];
  domain?: Domain;
  current: CurrentState = {
    sheet: "",
    test: {},
    step: {},
    stepIndex: -1,
  };
  results: TestResults = {
    executedTests: [],
    testsWithError: [],
    notCompletedTests: [],
    omittedTests: [],
    omittedSteps: [],
    usedAliases: [],
    usedElements: [],
  };
  report?: Report;

  constructor() {
    this.config = {
      ...Cypress.env(),
    } as SdtConfig;

    // Initialize actions with core actions
    this.actions = { ...coreActions };
    this.functions = { ...coreFunctions };
  }

  /**
   * Loads the appropriate extension based on the app environment variable.
   * @returns Promise resolving to the extension's default export
   * @throws Error if extension is not found or fails to load
   */
  private async loadExtension(): Promise<any> {
    const projectName = Cypress.env().app;

    if (!projectName) {
      throw new Error(
        "App environment variable is not set. Please specify which extension to load."
      );
    }

    const extensionLoader = Sdt.extensionMap[projectName];
    if (!extensionLoader) {
      throw new Error(
        `Extension '${projectName}' not found. Available extensions: ${Object.keys(
          Sdt.extensionMap
        ).join(", ")}`
      );
    }

    try {
      const extension = await extensionLoader();
      return extension.default;
    } catch (error) {
      throw new Error(
        `Failed to load extension '${projectName}': ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Initializes the SDT instance by loading extension, merging configurations,
   * and setting up test data.
   * @returns Promise resolving to the initialized SDT instance
   */
  async initialize(): Promise<Sdt> {
    try {
      // Load the appropriate extension
      this.extension = await this.loadExtension();

      // Merge extension configuration with base configuration
      this.config = {
        ...this.config,
        ...this.extension?.config,
      };

      // Validate that required configuration is present
      if (!this.config.sdtData) {
        throw new Error(
          "SDT data is missing from configuration. Please ensure sdtData is properly configured."
        );
      }

      // Set up extension components
      this.setup = this.extension?.setup;
      this.icons = this.extension?.icons;

      // Merge actions and functions (extension overrides core)
      this.actions = {
        ...this.actions, // Already initialized with coreActions in constructor
        ...this.extension?.actions,
      };
      this.functions = {
        ...this.functions, // Already initialized with coreFunctions in constructor
        ...this.extension?.functions,
      };

      // Set up handlers
      this.apiHandler = this.extension?.apiHandler;
      this.spyHandler = this.extension?.spyHandler;
      this.apiInterceptors = this.extension?.apiInterceptors;

      // Deep clone test data to prevent mutations
      this.data.tests = structuredClone(this.config.sdtData.tests);
      this.data.scripts = structuredClone(this.config.sdtData.scripts);
      this.data.tables = structuredClone(this.config.sdtData.tables);
      this.data.aliases = structuredClone(this.config.sdtData.aliases);
      this.data.elements = structuredClone(this.config.sdtData.elements);

      // Initialize reporting
      this.report = new Report(this.config);

      // Process and filter tests based on run flags
      this.setTests();

      return this;
    } catch (error) {
      throw new Error(
        `Failed to initialize SDT: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Processes test run flags and determines which tests to execute.
   * Priority order: z (highest) > y > x (lowest)
   * Special flags: "all" sets all tests in sheet to "z", "none" clears all flags in sheet
   */
  setTests(): void {
    // Process special run flags first
    this.data.tests.forEach((test) => {
      if (test.runFlag === "all") {
        // Set all tests in the same sheet to highest priority
        this.data.tests
          .filter((t) => t.sheet === test.sheet)
          .forEach((t) => (t.runFlag = "z"));
      }
      if (test.runFlag === "none") {
        // Clear run flags for all tests in the same sheet
        this.data.tests
          .filter((t) => t.sheet === test.sheet)
          .forEach((t) => (t.runFlag = null));
      }
    });

    // Categorize tests by priority
    const zPriorityTests = this.data.tests.filter(
      (test) => test.runFlag === "z"
    );
    const yPriorityTests = this.data.tests.filter(
      (test) => test.runFlag === "y"
    );
    const xPriorityTests = this.data.tests.filter(
      (test) => test.runFlag === "x"
    );
    const omittedTests = this.data.tests.filter((test) => !test.runFlag);

    // Determine which tests to run based on priority hierarchy
    if (zPriorityTests.length > 0) {
      // Run only highest priority tests
      this.testsToRun = [...zPriorityTests];
      this.results.omittedTests = [
        ...xPriorityTests,
        ...yPriorityTests,
        ...omittedTests,
      ];
    } else if (yPriorityTests.length > 0) {
      // Run medium priority tests (note: zPriorityTests is empty here)
      this.testsToRun = [...yPriorityTests];
      this.results.omittedTests = [...xPriorityTests, ...omittedTests];
    } else {
      // Run low priority tests only
      this.testsToRun = [...xPriorityTests];
      this.results.omittedTests = [...omittedTests];
    }

    // Log test execution plan
    console.log(`SDT Test Execution Plan:
      - Tests to run: ${this.testsToRun.length}
      - Tests omitted: ${this.results.omittedTests.length}
      - Priority breakdown: z=${zPriorityTests.length}, y=${yPriorityTests.length}, x=${xPriorityTests.length}`);
  }

  /**
   * Resets and initializes the domain with entities from data tables.
   * The domain manages test data entities and their relationships.
   */
  resetDomain(): void {
    try {
      // Merge all table data into a single entities object
      const entities = Object.values(this.data.tables).reduce((acc, table) => {
        return {
          ...(acc as object),
          ...(table as object),
        };
      }, {});

      this.domain = new Domain(entities);
      console.log(`Domain reset with ${Object.keys(entities).length} entities`);
    } catch (error) {
      console.error("Failed to reset domain:", error);
      throw new Error(
        `Domain reset failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Executes all tests that are scheduled to run.
   * Tests are organized by sheet and executed using Cypress describe/it blocks.
   */
  run(): void {
    if (this.testsToRun.length === 0) {
      console.warn("No tests scheduled to run. Check your test run flags.");
      return;
    }

    // Group tests by sheet for organized execution
    const sheets = [...new Set(this.testsToRun.map((test) => test.sheet))];

    console.log(
      `Starting test execution across ${sheets.length} sheet(s): ${sheets.join(
        ", "
      )}`
    );

    sheets.forEach((sheetName: string) => {
      const sheetTests = this.testsToRun.filter(
        (test) => test.sheet === sheetName
      );

      describe(sheetName, () => {
        sheetTests.forEach((testData) => {
          try {
            const test = new Test(testData);
            test.run();
          } catch (error) {
            console.error(
              `Failed to create/run test in sheet ${sheetName}:`,
              error
            );
            // Continue with other tests even if one fails to initialize
          }
        });
      });
    });
  }
}
