import cypressHelper from "@/app/helpers/cypress";

/**
 * Screenshot utility class for the SDT framework
 *
 * This class provides screenshot functionality integrated with the SDT test execution flow.
 * Screenshots are automatically named based on the current test context and stored with
 * metadata for reporting purposes.
 *
 * Key features:
 * - Automatic filename generation based on test sheet and step context
 * - Integration with Cypress screenshot API
 * - Path tracking for result reporting
 * - Animation/timer disabling for consistent screenshots
 *
 * Usage contexts:
 * - Manual screenshots via step.screenshot = true
 * - Automatic screenshots on test titles (if configured)
 * - Error screenshots on test failures (if configured)
 */
export default class Screenshot {
  /**
   * Takes a screenshot of the current application state
   *
   * This method handles the complete screenshot workflow:
   * 1. Applies a brief delay to ensure UI stability
   * 2. Generates a contextual filename based on current test/step
   * 3. Captures the screenshot with optimized settings
   * 4. Stores the screenshot path for reporting integration
   *
   * Screenshot naming convention:
   * - With row number: [SheetName][RowNumber].png
   * - Without row number: [SheetName].png
   *
   * The screenshot is processed by Cypress's after:screenshot hook
   * (configured in cypress.config.ts) which moves it to the appropriate
   * results folder and handles error screenshot naming.
   *
   * @throws {Error} If screenshot capture fails or SDT context is invalid
   */
  public static takeScreenshot(): void {
    try {
      // Apply delay to ensure UI has settled before screenshot
      // This helps avoid capturing transitional states or loading indicators
      cypressHelper.delay();

      // Validate that we have the required SDT context
      if (!Cypress.sdt?.current?.test?.sheet) {
        throw new Error("Invalid SDT context: missing test sheet information");
      }

      // Generate filename based on current test context
      const filename = Screenshot.generateFilename();

      // Store filename in current step for reporting integration
      Cypress.sdt.current.step.screenshotFilename = filename;

      // Capture screenshot with optimized settings
      cy.screenshot(filename, {
        // Disable CSS animations and timers for consistent screenshots
        disableTimersAndAnimations: true,

        // Callback to capture the actual file path after screenshot is taken
        onAfterScreenshot: (_$el, props) => {
          // Store the actual file path for reporting and cleanup
          Cypress.sdt.current.step.screenshotPath = props.path;
        },
      });
    } catch (error) {
      // Log error but don't fail the test - screenshots are supplementary
      console.error("Screenshot capture failed:", error);

      // Optionally still try to take a basic screenshot with timestamp fallback
      const fallbackFilename = `error-${Date.now()}.png`;
      cy.screenshot(fallbackFilename, {
        disableTimersAndAnimations: true,
        capture: "viewport", // Capture only viewport on error
      });
    }
  }

  /**
   * Generates a contextual filename for the screenshot
   *
   * The filename format depends on whether the current step has a row number:
   * - With row number: [SheetName][RowNumber].png
   * - Without row number: [SheetName].png
   *
   * This naming convention allows for:
   * - Easy identification of which test sheet the screenshot belongs to
   * - Correlation with specific test steps when row numbers are available
   * - Consistent sorting and organization in result folders
   *
   * @returns {string} The generated filename for the screenshot
   * @private
   */
  private static generateFilename(): string {
    const sheet = Cypress.sdt.current.test.sheet;
    const rowNumber = Cypress.sdt.current.step.rowNumber;

    // Sanitize sheet name to ensure valid filename
    const sanitizedSheet = Screenshot.sanitizeFilename(sheet);

    if (rowNumber) {
      return `[${sanitizedSheet}][${rowNumber}].png`;
    } else {
      return `[${sanitizedSheet}].png`;
    }
  }

  /**
   * Sanitizes a string to be safe for use as a filename
   *
   * Removes or replaces characters that are invalid in filenames
   * across different operating systems.
   *
   * @param {string} filename - The original filename string
   * @returns {string} The sanitized filename
   * @private
   */
  private static sanitizeFilename(filename: string): string {
    if (!filename) return "unknown";

    // Replace invalid filename characters with underscores
    return filename
      .replace(/[<>:"/\\|?*]/g, "_") // Windows invalid chars
      .replace(/\s+/g, "_") // Replace spaces with underscores
      .replace(/_{2,}/g, "_") // Replace multiple underscores with single
      .trim();
  }
}
